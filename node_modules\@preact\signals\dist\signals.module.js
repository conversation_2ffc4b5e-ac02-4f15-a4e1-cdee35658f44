import{Component as i,options as n,isValidElement as r}from"preact";import{useMemo as t,useRef as f,useEffect as o}from"preact/hooks";import{Signal as u,computed as e,effect as a,signal as c,batch as v}from"@preact/signals-core";export{Signal,batch,computed,effect,signal,untracked}from"@preact/signals-core";var s,l;function d(i,r){n[i]=r.bind(null,n[i]||function(){})}function h(i){if(l)l();l=i&&i.S()}function p(i){var n=this,f=i.data,o=useSignal(f);o.value=f;var u=t(function(){var i=n,t=n.__v;while(t=t.__)if(t.__c){t.__c.__$f|=4;break}var f=e(function(){var i=o.value.value;return 0===i?0:!0===i?"":i||""}),c=e(function(){var i;return r(f.value)||3!==(null==(i=n.base)?void 0:i.nodeType)});n.__$u.c=function(){var i;if(!r(u.peek())&&3===(null==(i=n.base)?void 0:i.nodeType))n.base.data=u.peek();else{n.__$f|=1;n.setState({})}};a(function(){if(!m)m=this.N;this.N=A;if(c.value&&i.base)i.base.data=f.value});return f},[]);return u.value}p.displayName="_st";Object.defineProperties(u.prototype,{constructor:{configurable:!0,value:void 0},type:{configurable:!0,value:p},props:{configurable:!0,get:function(){return{data:this}}},__b:{configurable:!0,value:1}});d("__b",function(i,n){if("string"==typeof n.type){var r,t=n.props;for(var f in t)if("children"!==f){var o=t[f];if(o instanceof u){if(!r)n.__np=r={};r[f]=o;t[f]=o.peek()}}}i(n)});d("__r",function(i,n){h();var r,t=n.__c;if(t){t.__$f&=-2;if(void 0===(r=t.__$u))t.__$u=r=function(i){var n;a(function(){n=this});n.c=function(){t.__$f|=1;t.setState({})};return n}()}s=t;h(r);i(n)});d("__e",function(i,n,r,t){h();s=void 0;i(n,r,t)});d("diffed",function(i,n){h();s=void 0;var r;if("string"==typeof n.type&&(r=n.__e)){var t=n.__np,f=n.props;if(t){var o=r.U;if(o)for(var u in o){var e=o[u];if(void 0!==e&&!(u in t)){e.d();o[u]=void 0}}else r.U=o={};for(var a in t){var c=o[a],v=t[a];if(void 0===c){c=_(r,a,v,f);o[a]=c}else c.o(v,f)}}}i(n)});function _(i,n,r,t){var f=n in i&&void 0===i.ownerSVGElement,o=c(r);return{o:function(i,n){o.value=i;t=n},d:a(function(){if(!m)m=this.N;this.N=A;var r=o.value.value;if(t[n]!==r){t[n]=r;if(f)i[n]=r;else if(r)i.setAttribute(n,r);else i.removeAttribute(n)}})}}d("unmount",function(i,n){if("string"==typeof n.type){var r=n.__e;if(r){var t=r.U;if(t){r.U=void 0;for(var f in t){var o=t[f];if(o)o.d()}}}}else{var u=n.__c;if(u){var e=u.__$u;if(e){u.__$u=void 0;e.d()}}}i(n)});d("__h",function(i,n,r,t){if(t<3||9===t)n.__$f|=2;i(n,r,t)});i.prototype.shouldComponentUpdate=function(i,n){var r=this.__$u;if(!(r&&void 0!==r.s||4&this.__$f))return!0;if(3&this.__$f)return!0;for(var t in n)return!0;for(var f in i)if("__source"!==f&&i[f]!==this.props[f])return!0;for(var o in this.props)if(!(o in i))return!0;return!1};function useSignal(i){return t(function(){return c(i)},[])}function useComputed(i){var n=f(i);n.current=i;s.__$f|=4;return t(function(){return e(function(){return n.current()})},[])}var m,g=[],b=[],k="undefined"==typeof requestAnimationFrame?setTimeout:requestAnimationFrame,y=function(i){queueMicrotask(function(){queueMicrotask(i)})};function q(){v(function(){var i;while(i=g.shift())m.call(i)})}function w(){if(1===g.push(this))(n.requestAnimationFrame||k)(q)}function x(){v(function(){var i;while(i=b.shift())m.call(i)})}function A(){if(1===b.push(this))(n.requestAnimationFrame||y)(x)}function useSignalEffect(i){var n=f(i);n.current=i;o(function(){return a(function(){if(!m)m=this.N;this.N=w;return n.current()})},[])}export{useComputed,useSignal,useSignalEffect};//# sourceMappingURL=signals.module.js.map
