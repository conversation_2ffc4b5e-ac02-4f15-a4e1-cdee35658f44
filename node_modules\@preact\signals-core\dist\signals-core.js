var i=Symbol.for("preact-signals");function t(){if(!(n>1)){var i,t=!1;while(void 0!==o){var r=o;o=void 0;h++;while(void 0!==r){var s=r.o;r.o=void 0;r.f&=-3;if(!(8&r.f)&&e(r))try{r.c()}catch(r){if(!t){i=r;t=!0}}r=s}}h=0;n--;if(t)throw i}else n--}var r=void 0,o=void 0,n=0,h=0,s=0;function f(i){if(void 0!==r){var t=i.n;if(void 0===t||t.t!==r){t={i:0,S:i,p:r.s,n:void 0,t:r,e:void 0,x:void 0,r:t};if(void 0!==r.s)r.s.n=t;r.s=t;i.n=t;if(32&r.f)i.S(t);return t}else if(-1===t.i){t.i=0;if(void 0!==t.n){t.n.p=t.p;if(void 0!==t.p)t.p.n=t.n;t.p=r.s;t.n=void 0;r.s.n=t;r.s=t}return t}}}function v(i){this.v=i;this.i=0;this.n=void 0;this.t=void 0}v.prototype.brand=i;v.prototype.h=function(){return!0};v.prototype.S=function(i){if(this.t!==i&&void 0===i.e){i.x=this.t;if(void 0!==this.t)this.t.e=i;this.t=i}};v.prototype.U=function(i){if(void 0!==this.t){var t=i.e,r=i.x;if(void 0!==t){t.x=r;i.e=void 0}if(void 0!==r){r.e=t;i.x=void 0}if(i===this.t)this.t=r}};v.prototype.subscribe=function(i){var t=this;return p(function(){var o=t.value,n=r;r=void 0;try{i(o)}finally{r=n}})};v.prototype.valueOf=function(){return this.value};v.prototype.toString=function(){return this.value+""};v.prototype.toJSON=function(){return this.value};v.prototype.peek=function(){var i=r;r=void 0;try{return this.value}finally{r=i}};Object.defineProperty(v.prototype,"value",{get:function(){var i=f(this);if(void 0!==i)i.i=this.i;return this.v},set:function(i){if(i!==this.v){if(h>100)throw new Error("Cycle detected");this.v=i;this.i++;s++;n++;try{for(var r=this.t;void 0!==r;r=r.x)r.t.N()}finally{t()}}}});function e(i){for(var t=i.s;void 0!==t;t=t.n)if(t.S.i!==t.i||!t.S.h()||t.S.i!==t.i)return!0;return!1}function u(i){for(var t=i.s;void 0!==t;t=t.n){var r=t.S.n;if(void 0!==r)t.r=r;t.S.n=t;t.i=-1;if(void 0===t.n){i.s=t;break}}}function d(i){var t=i.s,r=void 0;while(void 0!==t){var o=t.p;if(-1===t.i){t.S.U(t);if(void 0!==o)o.n=t.n;if(void 0!==t.n)t.n.p=o}else r=t;t.S.n=t.r;if(void 0!==t.r)t.r=void 0;t=o}i.s=r}function c(i){v.call(this,void 0);this.x=i;this.s=void 0;this.g=s-1;this.f=4}(c.prototype=new v).h=function(){this.f&=-3;if(1&this.f)return!1;if(32==(36&this.f))return!0;this.f&=-5;if(this.g===s)return!0;this.g=s;this.f|=1;if(this.i>0&&!e(this)){this.f&=-2;return!0}var i=r;try{u(this);r=this;var t=this.x();if(16&this.f||this.v!==t||0===this.i){this.v=t;this.f&=-17;this.i++}}catch(i){this.v=i;this.f|=16;this.i++}r=i;d(this);this.f&=-2;return!0};c.prototype.S=function(i){if(void 0===this.t){this.f|=36;for(var t=this.s;void 0!==t;t=t.n)t.S.S(t)}v.prototype.S.call(this,i)};c.prototype.U=function(i){if(void 0!==this.t){v.prototype.U.call(this,i);if(void 0===this.t){this.f&=-33;for(var t=this.s;void 0!==t;t=t.n)t.S.U(t)}}};c.prototype.N=function(){if(!(2&this.f)){this.f|=6;for(var i=this.t;void 0!==i;i=i.x)i.t.N()}};Object.defineProperty(c.prototype,"value",{get:function(){if(1&this.f)throw new Error("Cycle detected");var i=f(this);this.h();if(void 0!==i)i.i=this.i;if(16&this.f)throw this.v;return this.v}});function a(i){var o=i.u;i.u=void 0;if("function"==typeof o){n++;var h=r;r=void 0;try{o()}catch(t){i.f&=-2;i.f|=8;l(i);throw t}finally{r=h;t()}}}function l(i){for(var t=i.s;void 0!==t;t=t.n)t.S.U(t);i.x=void 0;i.s=void 0;a(i)}function y(i){if(r!==this)throw new Error("Out-of-order effect");d(this);r=i;this.f&=-2;if(8&this.f)l(this);t()}function w(i){this.x=i;this.u=void 0;this.s=void 0;this.o=void 0;this.f=32}w.prototype.c=function(){var i=this.S();try{if(8&this.f)return;if(void 0===this.x)return;var t=this.x();if("function"==typeof t)this.u=t}finally{i()}};w.prototype.S=function(){if(1&this.f)throw new Error("Cycle detected");this.f|=1;this.f&=-9;a(this);u(this);n++;var i=r;r=this;return y.bind(this,i)};w.prototype.N=function(){if(!(2&this.f)){this.f|=2;this.o=o;o=this}};w.prototype.d=function(){this.f|=8;if(!(1&this.f))l(this)};function p(i){var t=new w(i);try{t.c()}catch(i){t.d();throw i}return t.d.bind(t)}exports.Signal=v;exports.batch=function(i){if(n>0)return i();n++;try{return i()}finally{t()}};exports.computed=function(i){return new c(i)};exports.effect=p;exports.signal=function(i){return new v(i)};exports.untracked=function(i){var t=r;r=void 0;try{return i()}finally{r=t}};//# sourceMappingURL=signals-core.js.map
