var i,n,r=require("preact"),t=require("preact/hooks"),f=require("@preact/signals-core");function o(i,n){r.options[i]=n.bind(null,r.options[i]||function(){})}function e(i){if(n)n();n=i&&i.S()}function u(i){var n=this,o=i.data,e=useSignal(o);e.value=o;var u=t.useMemo(function(){var i=n,t=n.__v;while(t=t.__)if(t.__c){t.__c.__$f|=4;break}var o=f.computed(function(){var i=e.value.value;return 0===i?0:!0===i?"":i||""}),a=f.computed(function(){var i;return r.isValidElement(o.value)||3!==(null==(i=n.base)?void 0:i.nodeType)});n.__$u.c=function(){var i;if(!r.isValidElement(u.peek())&&3===(null==(i=n.base)?void 0:i.nodeType))n.base.data=u.peek();else{n.__$f|=1;n.setState({})}};f.effect(function(){if(!c)c=this.N;this.N=g;if(a.value&&i.base)i.base.data=o.value});return o},[]);return u.value}u.displayName="_st";Object.defineProperties(f.Signal.prototype,{constructor:{configurable:!0,value:void 0},type:{configurable:!0,value:u},props:{configurable:!0,get:function(){return{data:this}}},__b:{configurable:!0,value:1}});o("__b",function(i,n){if("string"==typeof n.type){var r,t=n.props;for(var o in t)if("children"!==o){var e=t[o];if(e instanceof f.Signal){if(!r)n.__np=r={};r[o]=e;t[o]=e.peek()}}}i(n)});o("__r",function(n,r){e();var t,o=r.__c;if(o){o.__$f&=-2;if(void 0===(t=o.__$u))o.__$u=t=function(i){var n;f.effect(function(){n=this});n.c=function(){o.__$f|=1;o.setState({})};return n}()}i=o;e(t);n(r)});o("__e",function(n,r,t,f){e();i=void 0;n(r,t,f)});o("diffed",function(n,r){e();i=void 0;var t;if("string"==typeof r.type&&(t=r.__e)){var f=r.__np,o=r.props;if(f){var u=t.U;if(u)for(var c in u){var v=u[c];if(void 0!==v&&!(c in f)){v.d();u[c]=void 0}}else t.U=u={};for(var s in f){var l=u[s],h=f[s];if(void 0===l){l=a(t,s,h,o);u[s]=l}else l.o(h,o)}}}n(r)});function a(i,n,r,t){var o=n in i&&void 0===i.ownerSVGElement,e=f.signal(r);return{o:function(i,n){e.value=i;t=n},d:f.effect(function(){if(!c)c=this.N;this.N=g;var r=e.value.value;if(t[n]!==r){t[n]=r;if(o)i[n]=r;else if(r)i.setAttribute(n,r);else i.removeAttribute(n)}})}}o("unmount",function(i,n){if("string"==typeof n.type){var r=n.__e;if(r){var t=r.U;if(t){r.U=void 0;for(var f in t){var o=t[f];if(o)o.d()}}}}else{var e=n.__c;if(e){var u=e.__$u;if(u){e.__$u=void 0;u.d()}}}i(n)});o("__h",function(i,n,r,t){if(t<3||9===t)n.__$f|=2;i(n,r,t)});r.Component.prototype.shouldComponentUpdate=function(i,n){var r=this.__$u;if(!(r&&void 0!==r.s||4&this.__$f))return!0;if(3&this.__$f)return!0;for(var t in n)return!0;for(var f in i)if("__source"!==f&&i[f]!==this.props[f])return!0;for(var o in this.props)if(!(o in i))return!0;return!1};function useSignal(i){return t.useMemo(function(){return f.signal(i)},[])}var c,v=[],s=[],l="undefined"==typeof requestAnimationFrame?setTimeout:requestAnimationFrame,h=function(i){queueMicrotask(function(){queueMicrotask(i)})};function d(){f.batch(function(){var i;while(i=v.shift())c.call(i)})}function p(){if(1===v.push(this))(r.options.requestAnimationFrame||l)(d)}function _(){f.batch(function(){var i;while(i=s.shift())c.call(i)})}function g(){if(1===s.push(this))(r.options.requestAnimationFrame||h)(_)}exports.Signal=f.Signal;exports.batch=f.batch;exports.computed=f.computed;exports.effect=f.effect;exports.signal=f.signal;exports.untracked=f.untracked;exports.useComputed=function(n){var r=t.useRef(n);r.current=n;i.__$f|=4;return t.useMemo(function(){return f.computed(function(){return r.current()})},[])};exports.useSignal=useSignal;exports.useSignalEffect=function(i){var n=t.useRef(i);n.current=i;t.useEffect(function(){return f.effect(function(){if(!c)c=this.N;this.N=p;return n.current()})},[])};//# sourceMappingURL=signals.js.map
