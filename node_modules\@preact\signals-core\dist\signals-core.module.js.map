{"version": 3, "file": "signals-core.module.js", "sources": ["../src/index.ts"], "sourcesContent": ["// An named symbol/brand for detecting Signal instances even when they weren't\n// created using the same signals library version.\nconst BRAND_SYMBOL = Symbol.for(\"preact-signals\");\n\n// Flags for Computed and Effect.\nconst RUNNING = 1 << 0;\nconst NOTIFIED = 1 << 1;\nconst OUTDATED = 1 << 2;\nconst DISPOSED = 1 << 3;\nconst HAS_ERROR = 1 << 4;\nconst TRACKING = 1 << 5;\n\n// A linked list node used to track dependencies (sources) and dependents (targets).\n// Also used to remember the source's last version number that the target saw.\ntype Node = {\n\t// A source whose value the target depends on.\n\t_source: Signal;\n\t_prevSource?: Node;\n\t_nextSource?: Node;\n\n\t// A target that depends on the source and should be notified when the source changes.\n\t_target: Computed | Effect;\n\t_prevTarget?: Node;\n\t_nextTarget?: Node;\n\n\t// The version number of the source that target has last seen. We use version numbers\n\t// instead of storing the source value, because source values can take arbitrary amount\n\t// of memory, and computeds could hang on to them forever because they're lazily evaluated.\n\t// Use the special value -1 to mark potentially unused but recyclable nodes.\n\t_version: number;\n\n\t// Used to remember & roll back the source's previous `._node` value when entering &\n\t// exiting a new evaluation context.\n\t_rollbackNode?: Node;\n};\n\nfunction startBatch() {\n\tbatchDepth++;\n}\n\nfunction endBatch() {\n\tif (batchDepth > 1) {\n\t\tbatchDepth--;\n\t\treturn;\n\t}\n\n\tlet error: unknown;\n\tlet hasError = false;\n\n\twhile (batchedEffect !== undefined) {\n\t\tlet effect: Effect | undefined = batchedEffect;\n\t\tbatchedEffect = undefined;\n\n\t\tbatchIteration++;\n\n\t\twhile (effect !== undefined) {\n\t\t\tconst next: Effect | undefined = effect._nextBatchedEffect;\n\t\t\teffect._nextBatchedEffect = undefined;\n\t\t\teffect._flags &= ~NOTIFIED;\n\n\t\t\tif (!(effect._flags & DISPOSED) && needsToRecompute(effect)) {\n\t\t\t\ttry {\n\t\t\t\t\teffect._callback();\n\t\t\t\t} catch (err) {\n\t\t\t\t\tif (!hasError) {\n\t\t\t\t\t\terror = err;\n\t\t\t\t\t\thasError = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\teffect = next;\n\t\t}\n\t}\n\tbatchIteration = 0;\n\tbatchDepth--;\n\n\tif (hasError) {\n\t\tthrow error;\n\t}\n}\n\n/**\n * Combine multiple value updates into one \"commit\" at the end of the provided callback.\n *\n * Batches can be nested and changes are only flushed once the outermost batch callback\n * completes.\n *\n * Accessing a signal that has been modified within a batch will reflect its updated\n * value.\n *\n * @param fn The callback function.\n * @returns The value returned by the callback.\n */\nfunction batch<T>(fn: () => T): T {\n\tif (batchDepth > 0) {\n\t\treturn fn();\n\t}\n\t/*@__INLINE__**/ startBatch();\n\ttry {\n\t\treturn fn();\n\t} finally {\n\t\tendBatch();\n\t}\n}\n\n// Currently evaluated computed or effect.\nlet evalContext: Computed | Effect | undefined = undefined;\n\n/**\n * Run a callback function that can access signal values without\n * subscribing to the signal updates.\n *\n * @param fn The callback function.\n * @returns The value returned by the callback.\n */\nfunction untracked<T>(fn: () => T): T {\n\tconst prevContext = evalContext;\n\tevalContext = undefined;\n\ttry {\n\t\treturn fn();\n\t} finally {\n\t\tevalContext = prevContext;\n\t}\n}\n\n// Effects collected into a batch.\nlet batchedEffect: Effect | undefined = undefined;\nlet batchDepth = 0;\nlet batchIteration = 0;\n\n// A global version number for signals, used for fast-pathing repeated\n// computed.peek()/computed.value calls when nothing has changed globally.\nlet globalVersion = 0;\n\nfunction addDependency(signal: Signal): Node | undefined {\n\tif (evalContext === undefined) {\n\t\treturn undefined;\n\t}\n\n\tlet node = signal._node;\n\tif (node === undefined || node._target !== evalContext) {\n\t\t/**\n\t\t * `signal` is a new dependency. Create a new dependency node, and set it\n\t\t * as the tail of the current context's dependency list. e.g:\n\t\t *\n\t\t * { A <-> B       }\n\t\t *         ↑     ↑\n\t\t *        tail  node (new)\n\t\t *               ↓\n\t\t * { A <-> B <-> C }\n\t\t *               ↑\n\t\t *              tail (evalContext._sources)\n\t\t */\n\t\tnode = {\n\t\t\t_version: 0,\n\t\t\t_source: signal,\n\t\t\t_prevSource: evalContext._sources,\n\t\t\t_nextSource: undefined,\n\t\t\t_target: evalContext,\n\t\t\t_prevTarget: undefined,\n\t\t\t_nextTarget: undefined,\n\t\t\t_rollbackNode: node,\n\t\t};\n\n\t\tif (evalContext._sources !== undefined) {\n\t\t\tevalContext._sources._nextSource = node;\n\t\t}\n\t\tevalContext._sources = node;\n\t\tsignal._node = node;\n\n\t\t// Subscribe to change notifications from this dependency if we're in an effect\n\t\t// OR evaluating a computed signal that in turn has subscribers.\n\t\tif (evalContext._flags & TRACKING) {\n\t\t\tsignal._subscribe(node);\n\t\t}\n\t\treturn node;\n\t} else if (node._version === -1) {\n\t\t// `signal` is an existing dependency from a previous evaluation. Reuse it.\n\t\tnode._version = 0;\n\n\t\t/**\n\t\t * If `node` is not already the current tail of the dependency list (i.e.\n\t\t * there is a next node in the list), then make the `node` the new tail. e.g:\n\t\t *\n\t\t * { A <-> B <-> C <-> D }\n\t\t *         ↑           ↑\n\t\t *        node   ┌─── tail (evalContext._sources)\n\t\t *         └─────│─────┐\n\t\t *               ↓     ↓\n\t\t * { A <-> C <-> D <-> B }\n\t\t *                     ↑\n\t\t *                    tail (evalContext._sources)\n\t\t */\n\t\tif (node._nextSource !== undefined) {\n\t\t\tnode._nextSource._prevSource = node._prevSource;\n\n\t\t\tif (node._prevSource !== undefined) {\n\t\t\t\tnode._prevSource._nextSource = node._nextSource;\n\t\t\t}\n\n\t\t\tnode._prevSource = evalContext._sources;\n\t\t\tnode._nextSource = undefined;\n\n\t\t\tevalContext._sources!._nextSource = node;\n\t\t\tevalContext._sources = node;\n\t\t}\n\n\t\t// We can assume that the currently evaluated effect / computed signal is already\n\t\t// subscribed to change notifications from `signal` if needed.\n\t\treturn node;\n\t}\n\treturn undefined;\n}\n\n/**\n * The base class for plain and computed signals.\n */\n// @ts-ignore: \"Cannot redeclare exported variable 'Signal'.\"\n//\n// A function with the same name is defined later, so we need to ignore TypeScript's\n// warning about a redeclared variable.\n//\n// The class is declared here, but later implemented with ES5-style prototypes.\n// This enables better control of the transpiled output size.\ndeclare class Signal<T = any> {\n\t/** @internal */\n\t_value: unknown;\n\n\t/**\n\t * @internal\n\t * Version numbers should always be >= 0, because the special value -1 is used\n\t * by Nodes to signify potentially unused but recyclable nodes.\n\t */\n\t_version: number;\n\n\t/** @internal */\n\t_node?: Node;\n\n\t/** @internal */\n\t_targets?: Node;\n\n\tconstructor(value?: T);\n\n\t/** @internal */\n\t_refresh(): boolean;\n\n\t/** @internal */\n\t_subscribe(node: Node): void;\n\n\t/** @internal */\n\t_unsubscribe(node: Node): void;\n\n\tsubscribe(fn: (value: T) => void): () => void;\n\n\tvalueOf(): T;\n\n\ttoString(): string;\n\n\ttoJSON(): T;\n\n\tpeek(): T;\n\n\tbrand: typeof BRAND_SYMBOL;\n\n\tget value(): T;\n\tset value(value: T);\n}\n\n/** @internal */\n// @ts-ignore: \"Cannot redeclare exported variable 'Signal'.\"\n//\n// A class with the same name has already been declared, so we need to ignore\n// TypeScript's warning about a redeclared variable.\n//\n// The previously declared class is implemented here with ES5-style prototypes.\n// This enables better control of the transpiled output size.\nfunction Signal(this: Signal, value?: unknown) {\n\tthis._value = value;\n\tthis._version = 0;\n\tthis._node = undefined;\n\tthis._targets = undefined;\n}\n\nSignal.prototype.brand = BRAND_SYMBOL;\n\nSignal.prototype._refresh = function () {\n\treturn true;\n};\n\nSignal.prototype._subscribe = function (node) {\n\tif (this._targets !== node && node._prevTarget === undefined) {\n\t\tnode._nextTarget = this._targets;\n\t\tif (this._targets !== undefined) {\n\t\t\tthis._targets._prevTarget = node;\n\t\t}\n\t\tthis._targets = node;\n\t}\n};\n\nSignal.prototype._unsubscribe = function (node) {\n\t// Only run the unsubscribe step if the signal has any subscribers to begin with.\n\tif (this._targets !== undefined) {\n\t\tconst prev = node._prevTarget;\n\t\tconst next = node._nextTarget;\n\t\tif (prev !== undefined) {\n\t\t\tprev._nextTarget = next;\n\t\t\tnode._prevTarget = undefined;\n\t\t}\n\t\tif (next !== undefined) {\n\t\t\tnext._prevTarget = prev;\n\t\t\tnode._nextTarget = undefined;\n\t\t}\n\t\tif (node === this._targets) {\n\t\t\tthis._targets = next;\n\t\t}\n\t}\n};\n\nSignal.prototype.subscribe = function (fn) {\n\treturn effect(() => {\n\t\tconst value = this.value;\n\n\t\tconst prevContext = evalContext;\n\t\tevalContext = undefined;\n\t\ttry {\n\t\t\tfn(value);\n\t\t} finally {\n\t\t\tevalContext = prevContext;\n\t\t}\n\t});\n};\n\nSignal.prototype.valueOf = function () {\n\treturn this.value;\n};\n\nSignal.prototype.toString = function () {\n\treturn this.value + \"\";\n};\n\nSignal.prototype.toJSON = function () {\n\treturn this.value;\n};\n\nSignal.prototype.peek = function () {\n\tconst prevContext = evalContext;\n\tevalContext = undefined;\n\ttry {\n\t\treturn this.value;\n\t} finally {\n\t\tevalContext = prevContext;\n\t}\n};\n\nObject.defineProperty(Signal.prototype, \"value\", {\n\tget(this: Signal) {\n\t\tconst node = addDependency(this);\n\t\tif (node !== undefined) {\n\t\t\tnode._version = this._version;\n\t\t}\n\t\treturn this._value;\n\t},\n\tset(this: Signal, value) {\n\t\tif (value !== this._value) {\n\t\t\tif (batchIteration > 100) {\n\t\t\t\tthrow new Error(\"Cycle detected\");\n\t\t\t}\n\n\t\t\tthis._value = value;\n\t\t\tthis._version++;\n\t\t\tglobalVersion++;\n\n\t\t\t/**@__INLINE__*/ startBatch();\n\t\t\ttry {\n\t\t\t\tfor (\n\t\t\t\t\tlet node = this._targets;\n\t\t\t\t\tnode !== undefined;\n\t\t\t\t\tnode = node._nextTarget\n\t\t\t\t) {\n\t\t\t\t\tnode._target._notify();\n\t\t\t\t}\n\t\t\t} finally {\n\t\t\t\tendBatch();\n\t\t\t}\n\t\t}\n\t},\n});\n\n/**\n * Create a new plain signal.\n *\n * @param value The initial value for the signal.\n * @returns A new signal.\n */\nexport function signal<T>(value: T): Signal<T>;\nexport function signal<T = undefined>(): Signal<T | undefined>;\nexport function signal<T>(value?: T): Signal<T> {\n\treturn new Signal(value);\n}\n\nfunction needsToRecompute(target: Computed | Effect): boolean {\n\t// Check the dependencies for changed values. The dependency list is already\n\t// in order of use. Therefore if multiple dependencies have changed values, only\n\t// the first used dependency is re-evaluated at this point.\n\tfor (\n\t\tlet node = target._sources;\n\t\tnode !== undefined;\n\t\tnode = node._nextSource\n\t) {\n\t\t// If there's a new version of the dependency before or after refreshing,\n\t\t// or the dependency has something blocking it from refreshing at all (e.g. a\n\t\t// dependency cycle), then we need to recompute.\n\t\tif (\n\t\t\tnode._source._version !== node._version ||\n\t\t\t!node._source._refresh() ||\n\t\t\tnode._source._version !== node._version\n\t\t) {\n\t\t\treturn true;\n\t\t}\n\t}\n\t// If none of the dependencies have changed values since last recompute then\n\t// there's no need to recompute.\n\treturn false;\n}\n\nfunction prepareSources(target: Computed | Effect) {\n\t/**\n\t * 1. Mark all current sources as re-usable nodes (version: -1)\n\t * 2. Set a rollback node if the current node is being used in a different context\n\t * 3. Point 'target._sources' to the tail of the doubly-linked list, e.g:\n\t *\n\t *    { undefined <- A <-> B <-> C -> undefined }\n\t *                   ↑           ↑\n\t *                   │           └──────┐\n\t * target._sources = A; (node is head)  │\n\t *                   ↓                  │\n\t * target._sources = C; (node is tail) ─┘\n\t */\n\tfor (\n\t\tlet node = target._sources;\n\t\tnode !== undefined;\n\t\tnode = node._nextSource\n\t) {\n\t\tconst rollbackNode = node._source._node;\n\t\tif (rollbackNode !== undefined) {\n\t\t\tnode._rollbackNode = rollbackNode;\n\t\t}\n\t\tnode._source._node = node;\n\t\tnode._version = -1;\n\n\t\tif (node._nextSource === undefined) {\n\t\t\ttarget._sources = node;\n\t\t\tbreak;\n\t\t}\n\t}\n}\n\nfunction cleanupSources(target: Computed | Effect) {\n\tlet node = target._sources;\n\tlet head = undefined;\n\n\t/**\n\t * At this point 'target._sources' points to the tail of the doubly-linked list.\n\t * It contains all existing sources + new sources in order of use.\n\t * Iterate backwards until we find the head node while dropping old dependencies.\n\t */\n\twhile (node !== undefined) {\n\t\tconst prev = node._prevSource;\n\n\t\t/**\n\t\t * The node was not re-used, unsubscribe from its change notifications and remove itself\n\t\t * from the doubly-linked list. e.g:\n\t\t *\n\t\t * { A <-> B <-> C }\n\t\t *         ↓\n\t\t *    { A <-> C }\n\t\t */\n\t\tif (node._version === -1) {\n\t\t\tnode._source._unsubscribe(node);\n\n\t\t\tif (prev !== undefined) {\n\t\t\t\tprev._nextSource = node._nextSource;\n\t\t\t}\n\t\t\tif (node._nextSource !== undefined) {\n\t\t\t\tnode._nextSource._prevSource = prev;\n\t\t\t}\n\t\t} else {\n\t\t\t/**\n\t\t\t * The new head is the last node seen which wasn't removed/unsubscribed\n\t\t\t * from the doubly-linked list. e.g:\n\t\t\t *\n\t\t\t * { A <-> B <-> C }\n\t\t\t *   ↑     ↑     ↑\n\t\t\t *   │     │     └ head = node\n\t\t\t *   │     └ head = node\n\t\t\t *   └ head = node\n\t\t\t */\n\t\t\thead = node;\n\t\t}\n\n\t\tnode._source._node = node._rollbackNode;\n\t\tif (node._rollbackNode !== undefined) {\n\t\t\tnode._rollbackNode = undefined;\n\t\t}\n\n\t\tnode = prev;\n\t}\n\n\ttarget._sources = head;\n}\n\ndeclare class Computed<T = any> extends Signal<T> {\n\t_fn: () => T;\n\t_sources?: Node;\n\t_globalVersion: number;\n\t_flags: number;\n\n\tconstructor(fn: () => T);\n\n\t_notify(): void;\n\tget value(): T;\n}\n\nfunction Computed(this: Computed, fn: () => unknown) {\n\tSignal.call(this, undefined);\n\n\tthis._fn = fn;\n\tthis._sources = undefined;\n\tthis._globalVersion = globalVersion - 1;\n\tthis._flags = OUTDATED;\n}\n\nComputed.prototype = new Signal() as Computed;\n\nComputed.prototype._refresh = function () {\n\tthis._flags &= ~NOTIFIED;\n\n\tif (this._flags & RUNNING) {\n\t\treturn false;\n\t}\n\n\t// If this computed signal has subscribed to updates from its dependencies\n\t// (TRACKING flag set) and none of them have notified about changes (OUTDATED\n\t// flag not set), then the computed value can't have changed.\n\tif ((this._flags & (OUTDATED | TRACKING)) === TRACKING) {\n\t\treturn true;\n\t}\n\tthis._flags &= ~OUTDATED;\n\n\tif (this._globalVersion === globalVersion) {\n\t\treturn true;\n\t}\n\tthis._globalVersion = globalVersion;\n\n\t// Mark this computed signal running before checking the dependencies for value\n\t// changes, so that the RUNNING flag can be used to notice cyclical dependencies.\n\tthis._flags |= RUNNING;\n\tif (this._version > 0 && !needsToRecompute(this)) {\n\t\tthis._flags &= ~RUNNING;\n\t\treturn true;\n\t}\n\n\tconst prevContext = evalContext;\n\ttry {\n\t\tprepareSources(this);\n\t\tevalContext = this;\n\t\tconst value = this._fn();\n\t\tif (\n\t\t\tthis._flags & HAS_ERROR ||\n\t\t\tthis._value !== value ||\n\t\t\tthis._version === 0\n\t\t) {\n\t\t\tthis._value = value;\n\t\t\tthis._flags &= ~HAS_ERROR;\n\t\t\tthis._version++;\n\t\t}\n\t} catch (err) {\n\t\tthis._value = err;\n\t\tthis._flags |= HAS_ERROR;\n\t\tthis._version++;\n\t}\n\tevalContext = prevContext;\n\tcleanupSources(this);\n\tthis._flags &= ~RUNNING;\n\treturn true;\n};\n\nComputed.prototype._subscribe = function (node) {\n\tif (this._targets === undefined) {\n\t\tthis._flags |= OUTDATED | TRACKING;\n\n\t\t// A computed signal subscribes lazily to its dependencies when it\n\t\t// gets its first subscriber.\n\t\tfor (\n\t\t\tlet node = this._sources;\n\t\t\tnode !== undefined;\n\t\t\tnode = node._nextSource\n\t\t) {\n\t\t\tnode._source._subscribe(node);\n\t\t}\n\t}\n\tSignal.prototype._subscribe.call(this, node);\n};\n\nComputed.prototype._unsubscribe = function (node) {\n\t// Only run the unsubscribe step if the computed signal has any subscribers.\n\tif (this._targets !== undefined) {\n\t\tSignal.prototype._unsubscribe.call(this, node);\n\n\t\t// Computed signal unsubscribes from its dependencies when it loses its last subscriber.\n\t\t// This makes it possible for unreferences subgraphs of computed signals to get garbage collected.\n\t\tif (this._targets === undefined) {\n\t\t\tthis._flags &= ~TRACKING;\n\n\t\t\tfor (\n\t\t\t\tlet node = this._sources;\n\t\t\t\tnode !== undefined;\n\t\t\t\tnode = node._nextSource\n\t\t\t) {\n\t\t\t\tnode._source._unsubscribe(node);\n\t\t\t}\n\t\t}\n\t}\n};\n\nComputed.prototype._notify = function () {\n\tif (!(this._flags & NOTIFIED)) {\n\t\tthis._flags |= OUTDATED | NOTIFIED;\n\n\t\tfor (\n\t\t\tlet node = this._targets;\n\t\t\tnode !== undefined;\n\t\t\tnode = node._nextTarget\n\t\t) {\n\t\t\tnode._target._notify();\n\t\t}\n\t}\n};\n\nObject.defineProperty(Computed.prototype, \"value\", {\n\tget(this: Computed) {\n\t\tif (this._flags & RUNNING) {\n\t\t\tthrow new Error(\"Cycle detected\");\n\t\t}\n\t\tconst node = addDependency(this);\n\t\tthis._refresh();\n\t\tif (node !== undefined) {\n\t\t\tnode._version = this._version;\n\t\t}\n\t\tif (this._flags & HAS_ERROR) {\n\t\t\tthrow this._value;\n\t\t}\n\t\treturn this._value;\n\t},\n});\n\n/**\n * An interface for read-only signals.\n */\ninterface ReadonlySignal<T = any> {\n\treadonly value: T;\n\tpeek(): T;\n\n\tsubscribe(fn: (value: T) => void): () => void;\n\tvalueOf(): T;\n\ttoString(): string;\n\ttoJSON(): T;\n\tbrand: typeof BRAND_SYMBOL;\n}\n\n/**\n * Create a new signal that is computed based on the values of other signals.\n *\n * The returned computed signal is read-only, and its value is automatically\n * updated when any signals accessed from within the callback function change.\n *\n * @param fn The effect callback.\n * @returns A new read-only signal.\n */\nfunction computed<T>(fn: () => T): ReadonlySignal<T> {\n\treturn new Computed(fn);\n}\n\nfunction cleanupEffect(effect: Effect) {\n\tconst cleanup = effect._cleanup;\n\teffect._cleanup = undefined;\n\n\tif (typeof cleanup === \"function\") {\n\t\t/*@__INLINE__**/ startBatch();\n\n\t\t// Run cleanup functions always outside of any context.\n\t\tconst prevContext = evalContext;\n\t\tevalContext = undefined;\n\t\ttry {\n\t\t\tcleanup();\n\t\t} catch (err) {\n\t\t\teffect._flags &= ~RUNNING;\n\t\t\teffect._flags |= DISPOSED;\n\t\t\tdisposeEffect(effect);\n\t\t\tthrow err;\n\t\t} finally {\n\t\t\tevalContext = prevContext;\n\t\t\tendBatch();\n\t\t}\n\t}\n}\n\nfunction disposeEffect(effect: Effect) {\n\tfor (\n\t\tlet node = effect._sources;\n\t\tnode !== undefined;\n\t\tnode = node._nextSource\n\t) {\n\t\tnode._source._unsubscribe(node);\n\t}\n\teffect._fn = undefined;\n\teffect._sources = undefined;\n\n\tcleanupEffect(effect);\n}\n\nfunction endEffect(this: Effect, prevContext?: Computed | Effect) {\n\tif (evalContext !== this) {\n\t\tthrow new Error(\"Out-of-order effect\");\n\t}\n\tcleanupSources(this);\n\tevalContext = prevContext;\n\n\tthis._flags &= ~RUNNING;\n\tif (this._flags & DISPOSED) {\n\t\tdisposeEffect(this);\n\t}\n\tendBatch();\n}\n\ntype EffectFn = () => void | (() => void);\n\ndeclare class Effect {\n\t_fn?: EffectFn;\n\t_cleanup?: () => void;\n\t_sources?: Node;\n\t_nextBatchedEffect?: Effect;\n\t_flags: number;\n\n\tconstructor(fn: EffectFn);\n\n\t_callback(): void;\n\t_start(): () => void;\n\t_notify(): void;\n\t_dispose(): void;\n}\n\nfunction Effect(this: Effect, fn: EffectFn) {\n\tthis._fn = fn;\n\tthis._cleanup = undefined;\n\tthis._sources = undefined;\n\tthis._nextBatchedEffect = undefined;\n\tthis._flags = TRACKING;\n}\n\nEffect.prototype._callback = function () {\n\tconst finish = this._start();\n\ttry {\n\t\tif (this._flags & DISPOSED) return;\n\t\tif (this._fn === undefined) return;\n\n\t\tconst cleanup = this._fn();\n\t\tif (typeof cleanup === \"function\") {\n\t\t\tthis._cleanup = cleanup;\n\t\t}\n\t} finally {\n\t\tfinish();\n\t}\n};\n\nEffect.prototype._start = function () {\n\tif (this._flags & RUNNING) {\n\t\tthrow new Error(\"Cycle detected\");\n\t}\n\tthis._flags |= RUNNING;\n\tthis._flags &= ~DISPOSED;\n\tcleanupEffect(this);\n\tprepareSources(this);\n\n\t/*@__INLINE__**/ startBatch();\n\tconst prevContext = evalContext;\n\tevalContext = this;\n\treturn endEffect.bind(this, prevContext);\n};\n\nEffect.prototype._notify = function () {\n\tif (!(this._flags & NOTIFIED)) {\n\t\tthis._flags |= NOTIFIED;\n\t\tthis._nextBatchedEffect = batchedEffect;\n\t\tbatchedEffect = this;\n\t}\n};\n\nEffect.prototype._dispose = function () {\n\tthis._flags |= DISPOSED;\n\n\tif (!(this._flags & RUNNING)) {\n\t\tdisposeEffect(this);\n\t}\n};\n\n/**\n * Create an effect to run arbitrary code in response to signal changes.\n *\n * An effect tracks which signals are accessed within the given callback\n * function `fn`, and re-runs the callback when those signals change.\n *\n * The callback may return a cleanup function. The cleanup function gets\n * run once, either when the callback is next called or when the effect\n * gets disposed, whichever happens first.\n *\n * @param fn The effect callback.\n * @returns A function for disposing the effect.\n */\nfunction effect(fn: EffectFn): () => void {\n\tconst effect = new Effect(fn);\n\ttry {\n\t\teffect._callback();\n\t} catch (err) {\n\t\teffect._dispose();\n\t\tthrow err;\n\t}\n\t// Return a bound function instead of a wrapper like `() => effect._dispose()`,\n\t// because bound functions seem to be just as fast and take up a lot less memory.\n\treturn effect._dispose.bind(effect);\n}\n\nexport { computed, effect, batch, untracked, Signal, ReadonlySignal };\n"], "names": ["BRAND_SYMBOL", "Symbol", "endBatch", "batchDepth", "error", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "batchedEffect", "effect", "batchIteration", "next", "_nextBatchedEffect", "_flags", "needsToRecompute", "_callback", "err", "batch", "fn", "evalContext", "untracked", "prevContext", "globalVersion", "addDependency", "signal", "node", "_node", "_target", "_version", "_source", "_prevSource", "_sources", "_nextSource", "_prevTarget", "_next<PERSON><PERSON><PERSON>", "_rollbackNode", "_subscribe", "Signal", "value", "this", "_value", "_targets", "prototype", "brand", "_refresh", "_unsubscribe", "prev", "subscribe", "_this", "valueOf", "toString", "toJSON", "peek", "Object", "defineProperty", "get", "set", "Error", "_notify", "target", "prepareSources", "rollbackNode", "cleanupSources", "head", "Computed", "call", "_fn", "_globalVersion", "OUTDATED", "computed", "cleanupEffect", "cleanup", "_cleanup", "disposeEffect", "endEffect", "Effect", "finish", "_start", "bind", "_dispose"], "mappings": "AAEA,IAAMA,EAAeC,WAAW,kBAsChC,SAASC,IACR,KAAIC,EAAa,GAAjB,CAKA,IAAIC,EACAC,GAAW,EAEf,WAAyBC,IAAlBC,EAA6B,CACnC,IAAIC,EAA6BD,EACjCA,OAAgBD,EAEhBG,IAEA,WAAkBH,IAAXE,EAAsB,CAC5B,IAAME,EAA2BF,EAAOG,EACxCH,EAAOG,OAAqBL,EAC5BE,EAAOI,IAAU,EAEjB,KApDc,EAoDRJ,EAAOI,IAAsBC,EAAiBL,GACnD,IACCA,EAAOM,GAMP,CALC,MAAOC,GACR,IAAKV,EAAU,CACdD,EAAQW,EACRV,GAAW,CACX,CACD,CAEFG,EAASE,CACT,CACD,CACDD,EAAiB,EACjBN,IAEA,GAAIE,EACH,MAAMD,CAjCN,MAFAD,GAqCF,CAcA,SAASa,EAASC,GACjB,GAAId,EAAa,EAChB,OAAOc,IA1DRd,IA6DA,IACC,OAAOc,GAGP,CAFA,QACAf,GACA,CACF,CAGA,IAAIgB,OAA6CZ,EASjD,SAASa,EAAaF,GACrB,IAAMG,EAAcF,EACpBA,OAAcZ,EACd,IACC,OAAOW,GAGP,CAFA,QACAC,EAAcE,CACd,CACF,CAGA,IAAIb,OAAoCD,EACpCH,EAAa,EACbM,EAAiB,EAIjBY,EAAgB,EAEpB,SAASC,EAAcC,GACtB,QAAoBjB,IAAhBY,EAAJ,CAIA,IAAIM,EAAOD,EAAOE,EAClB,QAAanB,IAATkB,GAAsBA,EAAKE,IAAYR,EAAa,CAavDM,EAAO,CACNG,EAAU,EACVC,EAASL,EACTM,EAAaX,EAAYY,EACzBC,OAAazB,EACboB,EAASR,EACTc,OAAa1B,EACb2B,OAAa3B,EACb4B,EAAeV,GAGhB,QAA6BlB,IAAzBY,EAAYY,EACfZ,EAAYY,EAASC,EAAcP,EAEpCN,EAAYY,EAAWN,EACvBD,EAAOE,EAAQD,EAIf,GAlKe,GAkKXN,EAAYN,EACfW,EAAOY,EAAWX,GAEnB,OAAOA,CACP,MAAUA,IAAmB,IAAnBA,EAAKG,EAAiB,CAEhCH,EAAKG,EAAW,EAehB,QAAyBrB,IAArBkB,EAAKO,EAA2B,CACnCP,EAAKO,EAAYF,EAAcL,EAAKK,EAEpC,QAAyBvB,IAArBkB,EAAKK,EACRL,EAAKK,EAAYE,EAAcP,EAAKO,EAGrCP,EAAKK,EAAcX,EAAYY,EAC/BN,EAAKO,OAAczB,EAEnBY,EAAYY,EAAUC,EAAcP,EACpCN,EAAYY,EAAWN,CACvB,CAID,OAAOA,CACP,CAzEA,CA2EF,CAgEA,SAASY,EAAqBC,GAC7BC,KAAKC,EAASF,EACdC,KAAKX,EAAW,EAChBW,KAAKb,OAAQnB,EACbgC,KAAKE,OAAWlC,CACjB,CAEA8B,EAAOK,UAAUC,MAAQ1C,EAEzBoC,EAAOK,UAAUE,EAAW,WAC3B,OACD,CAAA,EAEAP,EAAOK,UAAUN,EAAa,SAAUX,GACvC,GAAIc,KAAKE,IAAahB,QAA6BlB,IAArBkB,EAAKQ,EAA2B,CAC7DR,EAAKS,EAAcK,KAAKE,EACxB,QAAsBlC,IAAlBgC,KAAKE,EACRF,KAAKE,EAASR,EAAcR,EAE7Bc,KAAKE,EAAWhB,CAChB,CACF,EAEAY,EAAOK,UAAUG,EAAe,SAAUpB,GAEzC,QAAsBlB,IAAlBgC,KAAKE,EAAwB,CAChC,IAAMK,EAAOrB,EAAKQ,EACZtB,EAAOc,EAAKS,EAClB,QAAa3B,IAATuC,EAAoB,CACvBA,EAAKZ,EAAcvB,EACnBc,EAAKQ,OAAc1B,CACnB,CACD,QAAaA,IAATI,EAAoB,CACvBA,EAAKsB,EAAca,EACnBrB,EAAKS,OAAc3B,CACnB,CACD,GAAIkB,IAASc,KAAKE,EACjBF,KAAKE,EAAW9B,CAEjB,CACF,EAEA0B,EAAOK,UAAUK,UAAY,SAAU7B,GAAE8B,IAAAA,EACxCT,KAAA,OAAO9B,EAAO,WACb,IAAM6B,EAAQU,EAAKV,MAEbjB,EAAcF,EACpBA,OAAcZ,EACd,IACCW,EAAGoB,EAGH,CAFA,QACAnB,EAAcE,CACd,CACF,EACD,EAEAgB,EAAOK,UAAUO,QAAU,WAC1B,OAAOV,KAAKD,KACb,EAEAD,EAAOK,UAAUQ,SAAW,WAC3B,YAAYZ,MAAQ,EACrB,EAEAD,EAAOK,UAAUS,OAAS,WACzB,OAAWZ,KAACD,KACb,EAEAD,EAAOK,UAAUU,KAAO,WACvB,IAAM/B,EAAcF,EACpBA,OAAcZ,EACd,IACC,OAAOgC,KAAKD,KAGZ,CAFA,QACAnB,EAAcE,CACd,CACF,EAEAgC,OAAOC,eAAejB,EAAOK,UAAW,QAAS,CAChDa,IAAGA,WACF,IAAM9B,EAAOF,EAAcgB,MAC3B,QAAahC,IAATkB,EACHA,EAAKG,EAAWW,KAAKX,EAEtB,OAAWW,KAACC,CACb,EACAgB,aAAkBlB,GACjB,GAAIA,IAAUC,KAAKC,EAAQ,CAC1B,GAAI9B,EAAiB,IACpB,MAAM,IAAI+C,MAAM,kBAGjBlB,KAAKC,EAASF,EACdC,KAAKX,IACLN,IA7UFlB,IAgVE,IACC,IACC,IAAIqB,EAAOc,KAAKE,OACPlC,IAATkB,EACAA,EAAOA,EAAKS,EAEZT,EAAKE,EAAQ+B,GAId,CAFA,QACAvD,GACA,CACD,CACF,IAWK,SAAUqB,EAAUc,GACzB,OAAW,IAAAD,EAAOC,EACnB,CAEA,SAASxB,EAAiB6C,GAIzB,IACC,IAAIlC,EAAOkC,EAAO5B,OACTxB,IAATkB,EACAA,EAAOA,EAAKO,EAKZ,GACCP,EAAKI,EAAQD,IAAaH,EAAKG,IAC9BH,EAAKI,EAAQe,KACdnB,EAAKI,EAAQD,IAAaH,EAAKG,EAE/B,SAKF,OAAO,CACR,CAEA,SAASgC,EAAeD,GAavB,IACC,IAAIlC,EAAOkC,EAAO5B,OACTxB,IAATkB,EACAA,EAAOA,EAAKO,EACX,CACD,IAAM6B,EAAepC,EAAKI,EAAQH,EAClC,QAAqBnB,IAAjBsD,EACHpC,EAAKU,EAAgB0B,EAEtBpC,EAAKI,EAAQH,EAAQD,EACrBA,EAAKG,GAAY,EAEjB,QAAyBrB,IAArBkB,EAAKO,EAA2B,CACnC2B,EAAO5B,EAAWN,EAClB,KACA,CACD,CACF,CAEA,SAASqC,EAAeH,GACvB,IAAIlC,EAAOkC,EAAO5B,EACdgC,OAAOxD,EAOX,WAAgBA,IAATkB,EAAoB,CAC1B,IAAMqB,EAAOrB,EAAKK,EAUlB,IAAuB,IAAnBL,EAAKG,EAAiB,CACzBH,EAAKI,EAAQgB,EAAapB,GAE1B,QAAalB,IAATuC,EACHA,EAAKd,EAAcP,EAAKO,EAEzB,QAAyBzB,IAArBkB,EAAKO,EACRP,EAAKO,EAAYF,EAAcgB,CAEhC,MAWAiB,EAAOtC,EAGRA,EAAKI,EAAQH,EAAQD,EAAKU,EAC1B,QAA2B5B,IAAvBkB,EAAKU,EACRV,EAAKU,OAAgB5B,EAGtBkB,EAAOqB,CACP,CAEDa,EAAO5B,EAAWgC,CACnB,CAcA,SAASC,EAAyB9C,GACjCmB,EAAO4B,KAAK1B,UAAMhC,GAElBgC,KAAK2B,EAAMhD,EACXqB,KAAKR,OAAWxB,EAChBgC,KAAK4B,EAAiB7C,EAAgB,EACtCiB,KAAK1B,EA1gBW,CA2gBjB,EAEAmD,EAAStB,UAAY,IAAIL,GAENO,EAAW,WAC7BL,KAAK1B,IAAU,EAEf,GAphBe,EAohBX0B,KAAK1B,EACR,OACA,EAKD,GAthBgB,KAshBIuD,GAAf7B,KAAK1B,GACT,OAAO,EAER0B,KAAK1B,IAAU,EAEf,GAAI0B,KAAK4B,IAAmB7C,EAC3B,OAAO,EAERiB,KAAK4B,EAAiB7C,EAItBiB,KAAK1B,GAviBU,EAwiBf,GAAI0B,KAAKX,EAAW,IAAMd,EAAiByB,MAAO,CACjDA,KAAK1B,IAAU,EACf,OAAO,CACP,CAED,IAAMQ,EAAcF,EACpB,IACCyC,EAAerB,MACfpB,EAAcoB,KACd,IAAMD,EAAQC,KAAK2B,IACnB,GA9iBgB,GA+iBf3B,KAAK1B,GACL0B,KAAKC,IAAWF,GACE,IAAlBC,KAAKX,EACJ,CACDW,KAAKC,EAASF,EACdC,KAAK1B,IAAU,GACf0B,KAAKX,GACL,CAKD,CAJC,MAAOZ,GACRuB,KAAKC,EAASxB,EACduB,KAAK1B,GAzjBW,GA0jBhB0B,KAAKX,GACL,CACDT,EAAcE,EACdyC,EAAevB,MACfA,KAAK1B,IAAU,EACf,OACD,CAAA,EAEAmD,EAAStB,UAAUN,EAAa,SAAUX,GACzC,QAAsBlB,IAAlBgC,KAAKE,EAAwB,CAChCF,KAAK1B,GAAUuD,GAIf,IACC,IAAI3C,EAAOc,KAAKR,OACPxB,IAATkB,EACAA,EAAOA,EAAKO,EAEZP,EAAKI,EAAQO,EAAWX,EAEzB,CACDY,EAAOK,UAAUN,EAAW6B,KAAK1B,KAAMd,EACxC,EAEAuC,EAAStB,UAAUG,EAAe,SAAUpB,GAE3C,QAAsBlB,IAAlBgC,KAAKE,EAAwB,CAChCJ,EAAOK,UAAUG,EAAaoB,KAAK1B,KAAMd,GAIzC,QAAsBlB,IAAlBgC,KAAKE,EAAwB,CAChCF,KAAK1B,IAAU,GAEf,IACC,IAAIY,EAAOc,KAAKR,OACPxB,IAATkB,EACAA,EAAOA,EAAKO,EAEZP,EAAKI,EAAQgB,EAAapB,EAE3B,CACD,CACF,EAEAuC,EAAStB,UAAUgB,EAAU,WAC5B,KA5mBgB,EA4mBVnB,KAAK1B,GAAoB,CAC9B0B,KAAK1B,GAAUuD,EAEf,IACC,IAAI3C,EAAOc,KAAKE,OACPlC,IAATkB,EACAA,EAAOA,EAAKS,EAEZT,EAAKE,EAAQ+B,GAEd,CACF,EAEAL,OAAOC,eAAeU,EAAStB,UAAW,QAAS,CAClDa,eACC,GA5nBc,EA4nBVhB,KAAK1B,EACR,MAAM,IAAI4C,MAAM,kBAEjB,IAAMhC,EAAOF,EAAcgB,MAC3BA,KAAKK,IACL,QAAarC,IAATkB,EACHA,EAAKG,EAAWW,KAAKX,EAEtB,GAhoBgB,GAgoBZW,KAAK1B,EACR,MAAM0B,KAAKC,EAEZ,OAAOD,KAAKC,CACb,IA0BD,SAAS6B,EAAYnD,GACpB,OAAW,IAAA8C,EAAS9C,EACrB,CAEA,SAASoD,EAAc7D,GACtB,IAAM8D,EAAU9D,EAAO+D,EACvB/D,EAAO+D,OAAWjE,EAElB,GAAuB,mBAAZgE,EAAwB,CA1oBnCnE,IA8oBC,IAAMiB,EAAcF,EACpBA,OAAcZ,EACd,IACCgE,GASA,CARC,MAAOvD,GACRP,EAAOI,IAAU,EACjBJ,EAAOI,GAjrBO,EAkrBd4D,EAAchE,GACd,MAAMO,CACN,CAAA,QACAG,EAAcE,EACdlB,GACA,CACD,CACF,CAEA,SAASsE,EAAchE,GACtB,IACC,IAAIgB,EAAOhB,EAAOsB,OACTxB,IAATkB,EACAA,EAAOA,EAAKO,EAEZP,EAAKI,EAAQgB,EAAapB,GAE3BhB,EAAOyD,OAAM3D,EACbE,EAAOsB,OAAWxB,EAElB+D,EAAc7D,EACf,CAEA,SAASiE,EAAwBrD,GAChC,GAAIF,IAAgBoB,KACnB,UAAUkB,MAAM,uBAEjBK,EAAevB,MACfpB,EAAcE,EAEdkB,KAAK1B,IAAU,EACf,GAjtBgB,EAitBZ0B,KAAK1B,EACR4D,EAAclC,MAEfpC,GACD,CAmBA,SAASwE,EAAqBzD,GAC7BqB,KAAK2B,EAAMhD,EACXqB,KAAKiC,OAAWjE,EAChBgC,KAAKR,OAAWxB,EAChBgC,KAAK3B,OAAqBL,EAC1BgC,KAAK1B,EA3uBW,EA4uBjB,CAEA8D,EAAOjC,UAAU3B,EAAY,WAC5B,IAAM6D,EAASrC,KAAKsC,IACpB,IACC,GAnvBe,EAmvBXtC,KAAK1B,EAAmB,OAC5B,QAAiBN,IAAbgC,KAAK2B,EAAmB,OAE5B,IAAMK,EAAUhC,KAAK2B,IACrB,GAAuB,mBAAZK,EACVhC,KAAKiC,EAAWD,CAIjB,CAFA,QACAK,GACA,CACF,EAEAD,EAAOjC,UAAUmC,EAAS,WACzB,GAnwBe,EAmwBXtC,KAAK1B,EACR,UAAU4C,MAAM,kBAEjBlB,KAAK1B,GAtwBU,EAuwBf0B,KAAK1B,IAAU,EACfyD,EAAc/B,MACdqB,EAAerB,MAzuBfnC,IA4uBA,IAAMiB,EAAcF,EACpBA,EAAcoB,KACd,OAAOmC,EAAUI,KAAKvC,KAAMlB,EAC7B,EAEAsD,EAAOjC,UAAUgB,EAAU,WAC1B,KAjxBgB,EAixBVnB,KAAK1B,GAAoB,CAC9B0B,KAAK1B,GAlxBU,EAmxBf0B,KAAK3B,EAAqBJ,EAC1BA,EAAgB+B,IAChB,CACF,EAEAoC,EAAOjC,UAAUqC,EAAW,WAC3BxC,KAAK1B,GAvxBW,EAyxBhB,KA5xBe,EA4xBT0B,KAAK1B,GACV4D,EAAclC,KAEhB,EAeA,SAAS9B,EAAOS,GACf,IAAMT,EAAS,IAAIkE,EAAOzD,GAC1B,IACCT,EAAOM,GAIP,CAHC,MAAOC,GACRP,EAAOsE,IACP,MAAM/D,CACN,CAGD,OAAOP,EAAOsE,EAASD,KAAKrE,EAC7B,QAAA4B,YAAApB,WAAAoD,cAAA5D,YAAAe,YAAAJ"}