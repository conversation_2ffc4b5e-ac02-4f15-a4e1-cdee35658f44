"use strict";
(() => {
  // src/content/content.ts
  var sidebarVisible = false;
  var sidebarElement = null;
  var SIDEBAR_ID = "my-extension-sidebar-xyz";
  function createSidebar() {
    const existingSidebar = document.getElementById(SIDEBAR_ID);
    if (existingSidebar) {
      return existingSidebar;
    }
    const sidebar = document.createElement("div");
    sidebar.id = SIDEBAR_ID;
    sidebar.classList.add("my-extension-sidebar", "my-extension-sidebar-hidden");
    sidebar.innerHTML = `
    <div class="my-extension-sidebar-header">
      <h3>\u60AC\u6D6E\u7A97</h3>
      <button id="my-extension-sidebar-close">\xD7</button>
    </div>
    <div class="my-extension-sidebar-content">
      <p>\u8FD9\u91CC\u662F\u60AC\u6D6E\u7A97\u7684\u5185\u5BB9\u3002</p>
      <p>\u4F60\u53EF\u4EE5\u6DFB\u52A0\u66F4\u591A\u4EA4\u4E92\u5143\u7D20\u3002</p>
    </div>
  `;
    document.body.appendChild(sidebar);
    const closeButton = document.getElementById("my-extension-sidebar-close");
    if (closeButton) {
      closeButton.onclick = () => {
        toggleSidebar(false);
      };
    }
    return sidebar;
  }
  function toggleSidebar(forceState) {
    sidebarElement = sidebarElement || createSidebar();
    const shouldBeVisible = forceState !== void 0 ? forceState : !sidebarVisible;
    if (shouldBeVisible) {
      sidebarElement.classList.remove("my-extension-sidebar-hidden");
      sidebarElement.classList.add("my-extension-sidebar-visible");
      sidebarVisible = true;
    } else {
      sidebarElement.classList.remove("my-extension-sidebar-visible");
      sidebarElement.classList.add("my-extension-sidebar-hidden");
      sidebarVisible = false;
    }
  }
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "toggleSidebar") {
      toggleSidebar();
      sendResponse({ status: "Sidebar toggled", visible: sidebarVisible });
      return true;
    }
  });
  console.log("\u5185\u5BB9\u811A\u672C\u5DF2\u52A0\u8F7D\uFF01");
})();
