# Installation
> `npm install --save @types/chrome`

# Summary
This package contains type definitions for chrome (http://developer.chrome.com/extensions/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/chrome.

### Additional Details
 * Last updated: Tu<PERSON>, 23 Jul 2024 23:09:03 GMT
 * Dependencies: [@types/filesystem](https://npmjs.com/package/@types/filesystem), [@types/har-format](https://npmjs.com/package/@types/har-format)

# Credits
These definitions were written by [<PERSON>](https://github.com/matthewkimber), [otiai10](https://github.com/otiai10), [sreimer15](https://github.com/sreimer15), [<PERSON><PERSON><PERSON><PERSON>](https://github.com/Mat<PERSON>arlson), [ekinsol](https://github.com/ekinsol), [<PERSON>](https://github.com/echoabstract), [<PERSON><PERSON><PERSON><PERSON>](https://github.com/spasma), [bdbai](https://github.com/bdbai), [pokutuna](https://github.com/pokutuna), [<PERSON>an](https://github.com/JasonXian), [userTim](https://github.com/usertim), [Idan Zeierman](https://github.com/idan315), [Nicolas Rodriguez](https://github.com/nicolas377), [Ido Salomon](https://github.com/idosal), and [Federico Brigante](https://github.com/fregante).
