!function(i,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("preact"),require("preact/hooks"),require("@preact/signals-core")):"function"==typeof define&&define.amd?define(["exports","preact","preact/hooks","@preact/signals-core"],n):n((i||self).preactSignals={},i.preact,i.preactHooks,i.preactSignalsCore)}(this,function(i,n,r,t){var e,f;function o(i,r){n.options[i]=r.bind(null,n.options[i]||function(){})}function u(i){if(f)f();f=i&&i.S()}function a(i){var e=this,f=i.data,o=useSignal(f);o.value=f;var u=r.useMemo(function(){var i=e,r=e.__v;while(r=r.__)if(r.__c){r.__c.__$f|=4;break}var f=t.computed(function(){var i=o.value.value;return 0===i?0:!0===i?"":i||""}),a=t.computed(function(){var i;return n.isValidElement(f.value)||3!==(null==(i=e.base)?void 0:i.nodeType)});e.__$u.c=function(){var i;if(!n.isValidElement(u.peek())&&3===(null==(i=e.base)?void 0:i.nodeType))e.base.data=u.peek();else{e.__$f|=1;e.setState({})}};t.effect(function(){if(!v)v=this.N;this.N=b;if(a.value&&i.base)i.base.data=f.value});return f},[]);return u.value}a.displayName="_st";Object.defineProperties(t.Signal.prototype,{constructor:{configurable:!0,value:void 0},type:{configurable:!0,value:a},props:{configurable:!0,get:function(){return{data:this}}},__b:{configurable:!0,value:1}});o("__b",function(i,n){if("string"==typeof n.type){var r,e=n.props;for(var f in e)if("children"!==f){var o=e[f];if(o instanceof t.Signal){if(!r)n.__np=r={};r[f]=o;e[f]=o.peek()}}}i(n)});o("__r",function(i,n){u();var r,f=n.__c;if(f){f.__$f&=-2;if(void 0===(r=f.__$u))f.__$u=r=function(i){var n;t.effect(function(){n=this});n.c=function(){f.__$f|=1;f.setState({})};return n}()}e=f;u(r);i(n)});o("__e",function(i,n,r,t){u();e=void 0;i(n,r,t)});o("diffed",function(i,n){u();e=void 0;var r;if("string"==typeof n.type&&(r=n.__e)){var t=n.__np,f=n.props;if(t){var o=r.U;if(o)for(var a in o){var v=o[a];if(void 0!==v&&!(a in t)){v.d();o[a]=void 0}}else r.U=o={};for(var s in t){var l=o[s],d=t[s];if(void 0===l){l=c(r,s,d,f);o[s]=l}else l.o(d,f)}}}i(n)});function c(i,n,r,e){var f=n in i&&void 0===i.ownerSVGElement,o=t.signal(r);return{o:function(i,n){o.value=i;e=n},d:t.effect(function(){if(!v)v=this.N;this.N=b;var r=o.value.value;if(e[n]!==r){e[n]=r;if(f)i[n]=r;else if(r)i.setAttribute(n,r);else i.removeAttribute(n)}})}}o("unmount",function(i,n){if("string"==typeof n.type){var r=n.__e;if(r){var t=r.U;if(t){r.U=void 0;for(var e in t){var f=t[e];if(f)f.d()}}}}else{var o=n.__c;if(o){var u=o.__$u;if(u){o.__$u=void 0;u.d()}}}i(n)});o("__h",function(i,n,r,t){if(t<3||9===t)n.__$f|=2;i(n,r,t)});n.Component.prototype.shouldComponentUpdate=function(i,n){var r=this.__$u;if(!(r&&void 0!==r.s||4&this.__$f))return!0;if(3&this.__$f)return!0;for(var t in n)return!0;for(var e in i)if("__source"!==e&&i[e]!==this.props[e])return!0;for(var f in this.props)if(!(f in i))return!0;return!1};function useSignal(i){return r.useMemo(function(){return t.signal(i)},[])}var v,s=[],l=[],d="undefined"==typeof requestAnimationFrame?setTimeout:requestAnimationFrame,h=function(i){queueMicrotask(function(){queueMicrotask(i)})};function p(){t.batch(function(){var i;while(i=s.shift())v.call(i)})}function _(){if(1===s.push(this))(n.options.requestAnimationFrame||d)(p)}function g(){t.batch(function(){var i;while(i=l.shift())v.call(i)})}function b(){if(1===l.push(this))(n.options.requestAnimationFrame||h)(g)}i.Signal=t.Signal;i.batch=t.batch;i.computed=t.computed;i.effect=t.effect;i.signal=t.signal;i.untracked=t.untracked;i.useComputed=function(i){var n=r.useRef(i);n.current=i;e.__$f|=4;return r.useMemo(function(){return t.computed(function(){return n.current()})},[])};i.useSignal=useSignal;i.useSignalEffect=function(i){var n=r.useRef(i);n.current=i;r.useEffect(function(){return t.effect(function(){if(!v)v=this.N;this.N=_;return n.current()})},[])}});//# sourceMappingURL=signals.min.js.map
