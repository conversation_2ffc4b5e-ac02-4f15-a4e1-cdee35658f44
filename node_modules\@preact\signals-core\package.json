{"name": "@preact/signals-core", "version": "1.8.0", "license": "MIT", "description": "Manage state with style in every framework", "keywords": [], "authors": ["The Preact Authors (https://github.com/preactjs/signals/contributors)"], "repository": {"type": "git", "url": "https://github.com/preactjs/signals"}, "bugs": "https://github.com/preactjs/signals/issues", "homepage": "https://preactjs.com", "funding": {"type": "opencollective", "url": "https://opencollective.com/preact"}, "amdName": "preactSignalsCore", "main": "dist/signals-core.js", "module": "dist/signals-core.module.js", "unpkg": "dist/signals-core.min.js", "types": "dist/signals-core.d.ts", "source": "src/index.ts", "sideEffects": false, "exports": {".": {"types": "./dist/signals-core.d.ts", "browser": "./dist/signals-core.module.js", "import": "./dist/signals-core.mjs", "require": "./dist/signals-core.js"}}, "mangle": "../../mangle.json", "files": ["src", "dist", "CHANGELOG.md", "LICENSE", "README.md"], "publishConfig": {"provenance": true}, "scripts": {}}