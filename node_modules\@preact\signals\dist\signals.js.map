{"version": 3, "file": "signals.js", "sources": ["../src/index.ts"], "sourcesContent": ["import { options, Component, isValidElement } from \"preact\";\nimport { useRef, useMemo, useEffect } from \"preact/hooks\";\nimport {\n\tsignal,\n\tcomputed,\n\tbatch,\n\teffect,\n\tSignal,\n\ttype ReadonlySignal,\n\tuntracked,\n} from \"@preact/signals-core\";\nimport {\n\tVNode,\n\tOptionsTypes,\n\tHookFn,\n\tEffect,\n\tPropertyUpdater,\n\tAugmentedComponent,\n\tAugmentedElement as Element,\n} from \"./internal\";\n\nexport {\n\tsignal,\n\tcomputed,\n\tbatch,\n\teffect,\n\tSignal,\n\ttype ReadonlySignal,\n\tuntracked,\n};\n\nconst HAS_PENDING_UPDATE = 1 << 0;\nconst HAS_HOOK_STATE = 1 << 1;\nconst HAS_COMPUTEDS = 1 << 2;\n\n// Install a Preact options hook\nfunction hook<T extends OptionsTypes>(hookName: T, hookFn: HookFn<T>) {\n\t// @ts-ignore-next-line private options hooks usage\n\toptions[hookName] = hookFn.bind(null, options[hookName] || (() => {}));\n}\n\nlet currentComponent: AugmentedComponent | undefined;\nlet finishUpdate: (() => void) | undefined;\n\nfunction setCurrentUpdater(updater?: Effect) {\n\t// end tracking for the current update:\n\tif (finishUpdate) finishUpdate();\n\t// start tracking the new update:\n\tfinishUpdate = updater && updater._start();\n}\n\nfunction createUpdater(update: () => void) {\n\tlet updater!: Effect;\n\teffect(function (this: Effect) {\n\t\tupdater = this;\n\t});\n\tupdater._callback = update;\n\treturn updater;\n}\n\n/** @todo This may be needed for complex prop value detection. */\n// function isSignalValue(value: any): value is Signal {\n// \tif (typeof value !== \"object\" || value == null) return false;\n// \tif (value instanceof Signal) return true;\n// \t// @TODO: uncomment this when we land Reactive (ideally behind a brand check)\n// \t// for (let i in value) if (value[i] instanceof Signal) return true;\n// \treturn false;\n// }\n\n/**\n * A wrapper component that renders a Signal directly as a Text node.\n * @todo: in Preact 11, just decorate Signal with `type:null`\n */\nfunction SignalValue(this: AugmentedComponent, { data }: { data: Signal }) {\n\t// hasComputeds.add(this);\n\n\t// Store the props.data signal in another signal so that\n\t// passing a new signal reference re-runs the text computed:\n\tconst currentSignal = useSignal(data);\n\tcurrentSignal.value = data;\n\n\tconst s = useMemo(() => {\n\t\tlet self = this;\n\t\t// mark the parent component as having computeds so it gets optimized\n\t\tlet v = this.__v;\n\t\twhile ((v = v.__!)) {\n\t\t\tif (v.__c) {\n\t\t\t\tv.__c._updateFlags |= HAS_COMPUTEDS;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tconst wrappedSignal = computed(function (this: Effect) {\n\t\t\tlet data = currentSignal.value;\n\t\t\tlet s = data.value;\n\t\t\treturn s === 0 ? 0 : s === true ? \"\" : s || \"\";\n\t\t});\n\n\t\tconst isText = computed(\n\t\t\t() => isValidElement(wrappedSignal.value) || this.base?.nodeType !== 3\n\t\t);\n\n\t\tthis._updater!._callback = () => {\n\t\t\tif (isValidElement(s.peek()) || this.base?.nodeType !== 3) {\n\t\t\t\tthis._updateFlags |= HAS_PENDING_UPDATE;\n\t\t\t\tthis.setState({});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t(this.base as Text).data = s.peek();\n\t\t};\n\n\t\teffect(function (this: Effect) {\n\t\t\tif (!oldNotify) oldNotify = this._notify;\n\t\t\tthis._notify = notifyDomUpdates;\n\t\t\tconst val = wrappedSignal.value;\n\t\t\tif (isText.value && self.base) {\n\t\t\t\t(self.base as Text).data = val;\n\t\t\t}\n\t\t});\n\n\t\treturn wrappedSignal;\n\t}, []);\n\n\treturn s.value;\n}\nSignalValue.displayName = \"_st\";\n\nObject.defineProperties(Signal.prototype, {\n\tconstructor: { configurable: true, value: undefined },\n\ttype: { configurable: true, value: SignalValue },\n\tprops: {\n\t\tconfigurable: true,\n\t\tget() {\n\t\t\treturn { data: this };\n\t\t},\n\t},\n\t// Setting a VNode's _depth to 1 forces Preact to clone it before modifying:\n\t// https://github.com/preactjs/preact/blob/d7a433ee8463a7dc23a05111bb47de9ec729ad4d/src/diff/children.js#L77\n\t// @todo remove this for Preact 11\n\t__b: { configurable: true, value: 1 },\n});\n\n/** Inject low-level property/attribute bindings for Signals into Preact's diff */\nhook(OptionsTypes.DIFF, (old, vnode) => {\n\tif (typeof vnode.type === \"string\") {\n\t\tlet signalProps: Record<string, any> | undefined;\n\n\t\tlet props = vnode.props;\n\t\tfor (let i in props) {\n\t\t\tif (i === \"children\") continue;\n\n\t\t\tlet value = props[i];\n\t\t\tif (value instanceof Signal) {\n\t\t\t\tif (!signalProps) vnode.__np = signalProps = {};\n\t\t\t\tsignalProps[i] = value;\n\t\t\t\tprops[i] = value.peek();\n\t\t\t}\n\t\t}\n\t}\n\n\told(vnode);\n});\n\n/** Set up Updater before rendering a component */\nhook(OptionsTypes.RENDER, (old, vnode) => {\n\tsetCurrentUpdater();\n\n\tlet updater;\n\n\tlet component = vnode.__c;\n\tif (component) {\n\t\tcomponent._updateFlags &= ~HAS_PENDING_UPDATE;\n\n\t\tupdater = component._updater;\n\t\tif (updater === undefined) {\n\t\t\tcomponent._updater = updater = createUpdater(() => {\n\t\t\t\tcomponent._updateFlags |= HAS_PENDING_UPDATE;\n\t\t\t\tcomponent.setState({});\n\t\t\t});\n\t\t}\n\t}\n\n\tcurrentComponent = component;\n\tsetCurrentUpdater(updater);\n\told(vnode);\n});\n\n/** Finish current updater if a component errors */\nhook(OptionsTypes.CATCH_ERROR, (old, error, vnode, oldVNode) => {\n\tsetCurrentUpdater();\n\tcurrentComponent = undefined;\n\told(error, vnode, oldVNode);\n});\n\n/** Finish current updater after rendering any VNode */\nhook(OptionsTypes.DIFFED, (old, vnode) => {\n\tsetCurrentUpdater();\n\tcurrentComponent = undefined;\n\n\tlet dom: Element;\n\n\t// vnode._dom is undefined during string rendering,\n\t// so we use this to skip prop subscriptions during SSR.\n\tif (typeof vnode.type === \"string\" && (dom = vnode.__e as Element)) {\n\t\tlet props = vnode.__np;\n\t\tlet renderedProps = vnode.props;\n\t\tif (props) {\n\t\t\tlet updaters = dom._updaters;\n\t\t\tif (updaters) {\n\t\t\t\tfor (let prop in updaters) {\n\t\t\t\t\tlet updater = updaters[prop];\n\t\t\t\t\tif (updater !== undefined && !(prop in props)) {\n\t\t\t\t\t\tupdater._dispose();\n\t\t\t\t\t\t// @todo we could just always invoke _dispose() here\n\t\t\t\t\t\tupdaters[prop] = undefined;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tupdaters = {};\n\t\t\t\tdom._updaters = updaters;\n\t\t\t}\n\t\t\tfor (let prop in props) {\n\t\t\t\tlet updater = updaters[prop];\n\t\t\t\tlet signal = props[prop];\n\t\t\t\tif (updater === undefined) {\n\t\t\t\t\tupdater = createPropUpdater(dom, prop, signal, renderedProps);\n\t\t\t\t\tupdaters[prop] = updater;\n\t\t\t\t} else {\n\t\t\t\t\tupdater._update(signal, renderedProps);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\told(vnode);\n});\n\nfunction createPropUpdater(\n\tdom: Element,\n\tprop: string,\n\tpropSignal: Signal,\n\tprops: Record<string, any>\n): PropertyUpdater {\n\tconst setAsProperty =\n\t\tprop in dom &&\n\t\t// SVG elements need to go through `setAttribute` because they\n\t\t// expect things like SVGAnimatedTransformList instead of strings.\n\t\t// @ts-ignore\n\t\tdom.ownerSVGElement === undefined;\n\n\tconst changeSignal = signal(propSignal);\n\treturn {\n\t\t_update: (newSignal: Signal, newProps: typeof props) => {\n\t\t\tchangeSignal.value = newSignal;\n\t\t\tprops = newProps;\n\t\t},\n\t\t_dispose: effect(function (this: Effect) {\n\t\t\tif (!oldNotify) oldNotify = this._notify;\n\t\t\tthis._notify = notifyDomUpdates;\n\t\t\tconst value = changeSignal.value.value;\n\t\t\t// If Preact just rendered this value, don't render it again:\n\t\t\tif (props[prop] === value) return;\n\t\t\tprops[prop] = value;\n\t\t\tif (setAsProperty) {\n\t\t\t\t// @ts-ignore-next-line silly\n\t\t\t\tdom[prop] = value;\n\t\t\t} else if (value) {\n\t\t\t\tdom.setAttribute(prop, value);\n\t\t\t} else {\n\t\t\t\tdom.removeAttribute(prop);\n\t\t\t}\n\t\t}),\n\t};\n}\n\n/** Unsubscribe from Signals when unmounting components/vnodes */\nhook(OptionsTypes.UNMOUNT, (old, vnode: VNode) => {\n\tif (typeof vnode.type === \"string\") {\n\t\tlet dom = vnode.__e as Element | undefined;\n\t\t// vnode._dom is undefined during string rendering\n\t\tif (dom) {\n\t\t\tconst updaters = dom._updaters;\n\t\t\tif (updaters) {\n\t\t\t\tdom._updaters = undefined;\n\t\t\t\tfor (let prop in updaters) {\n\t\t\t\t\tlet updater = updaters[prop];\n\t\t\t\t\tif (updater) updater._dispose();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t} else {\n\t\tlet component = vnode.__c;\n\t\tif (component) {\n\t\t\tconst updater = component._updater;\n\t\t\tif (updater) {\n\t\t\t\tcomponent._updater = undefined;\n\t\t\t\tupdater._dispose();\n\t\t\t}\n\t\t}\n\t}\n\told(vnode);\n});\n\n/** Mark components that use hook state so we can skip sCU optimization. */\nhook(OptionsTypes.HOOK, (old, component, index, type) => {\n\tif (type < 3 || type === 9)\n\t\t(component as AugmentedComponent)._updateFlags |= HAS_HOOK_STATE;\n\told(component, index, type);\n});\n\n/**\n * Auto-memoize components that use Signals/Computeds.\n * Note: Does _not_ optimize components that use hook/class state.\n */\nComponent.prototype.shouldComponentUpdate = function (\n\tthis: AugmentedComponent,\n\tprops,\n\tstate\n) {\n\t// @todo: Once preactjs/preact#3671 lands, this could just use `currentUpdater`:\n\tconst updater = this._updater;\n\tconst hasSignals = updater && updater._sources !== undefined;\n\n\t// let reason;\n\t// if (!hasSignals && !hasComputeds.has(this)) {\n\t// \treason = \"no signals or computeds\";\n\t// } else if (hasPendingUpdate.has(this)) {\n\t// \treason = \"has pending update\";\n\t// } else if (hasHookState.has(this)) {\n\t// \treason = \"has hook state\";\n\t// }\n\t// if (reason) {\n\t// \tif (!this) reason += \" (`this` bug)\";\n\t// \tconsole.log(\"not optimizing\", this?.constructor?.name, \": \", reason, {\n\t// \t\tdetails: {\n\t// \t\t\thasSignals,\n\t// \t\t\thasComputeds: hasComputeds.has(this),\n\t// \t\t\thasPendingUpdate: hasPendingUpdate.has(this),\n\t// \t\t\thasHookState: hasHookState.has(this),\n\t// \t\t\tdeps: Array.from(updater._deps),\n\t// \t\t\tupdater,\n\t// \t\t},\n\t// \t});\n\t// }\n\n\t// if this component used no signals or computeds, update:\n\tif (!hasSignals && !(this._updateFlags & HAS_COMPUTEDS)) return true;\n\n\t// if there is a pending re-render triggered from Signals,\n\t// or if there is hook or class state, update:\n\tif (this._updateFlags & (HAS_PENDING_UPDATE | HAS_HOOK_STATE)) return true;\n\n\t// @ts-ignore\n\tfor (let i in state) return true;\n\n\t// if any non-Signal props changed, update:\n\tfor (let i in props) {\n\t\tif (i !== \"__source\" && props[i] !== this.props[i]) return true;\n\t}\n\tfor (let i in this.props) if (!(i in props)) return true;\n\n\t// this is a purely Signal-driven component, don't update:\n\treturn false;\n};\n\nexport function useSignal<T>(value: T): Signal<T>;\nexport function useSignal<T = undefined>(): Signal<T | undefined>;\nexport function useSignal<T>(value?: T) {\n\treturn useMemo(() => signal<T | undefined>(value), []);\n}\n\nexport function useComputed<T>(compute: () => T) {\n\tconst $compute = useRef(compute);\n\t$compute.current = compute;\n\t(currentComponent as AugmentedComponent)._updateFlags |= HAS_COMPUTEDS;\n\treturn useMemo(() => computed<T>(() => $compute.current()), []);\n}\n\nlet oldNotify: (this: Effect) => void,\n\teffectsQueue: Array<Effect> = [],\n\tdomQueue: Array<Effect> = [];\n\nconst deferEffects =\n\ttypeof requestAnimationFrame === \"undefined\"\n\t\t? setTimeout\n\t\t: requestAnimationFrame;\n\nconst deferDomUpdates = (cb: any) => {\n\tqueueMicrotask(() => {\n\t\tqueueMicrotask(cb);\n\t});\n};\n\nfunction flushEffects() {\n\tbatch(() => {\n\t\tlet inst: Effect | undefined;\n\t\twhile ((inst = effectsQueue.shift())) {\n\t\t\toldNotify.call(inst);\n\t\t}\n\t});\n}\n\nfunction notifyEffects(this: Effect) {\n\tif (effectsQueue.push(this) === 1) {\n\t\t(options.requestAnimationFrame || deferEffects)(flushEffects);\n\t}\n}\n\nfunction flushDomUpdates() {\n\tbatch(() => {\n\t\tlet inst: Effect | undefined;\n\t\twhile ((inst = domQueue.shift())) {\n\t\t\toldNotify.call(inst);\n\t\t}\n\t});\n}\n\nfunction notifyDomUpdates(this: Effect) {\n\tif (domQueue.push(this) === 1) {\n\t\t(options.requestAnimationFrame || deferDomUpdates)(flushDomUpdates);\n\t}\n}\n\nexport function useSignalEffect(cb: () => void | (() => void)) {\n\tconst callback = useRef(cb);\n\tcallback.current = cb;\n\n\tuseEffect(() => {\n\t\treturn effect(function (this: Effect) {\n\t\t\tif (!oldNotify) oldNotify = this._notify;\n\t\t\tthis._notify = notifyEffects;\n\t\t\treturn callback.current();\n\t\t});\n\t}, []);\n}\n\n/**\n * @todo Determine which Reactive implementation we'll be using.\n * @internal\n */\n// export function useReactive<T extends object>(value: T): Reactive<T> {\n// \treturn useMemo(() => reactive<T>(value), []);\n// }\n\n/**\n * @internal\n * Update a Reactive's using the properties of an object or other Reactive.\n * Also works for Signals.\n * @example\n *   // Update a Reactive with Object.assign()-like syntax:\n *   const r = reactive({ name: \"Alice\" });\n *   update(r, { name: \"Bob\" });\n *   update(r, { age: 42 }); // property 'age' does not exist in type '{ name?: string }'\n *   update(r, 2); // '2' has no properties in common with '{ name?: string }'\n *   console.log(r.name.value); // \"Bob\"\n *\n * @example\n *   // Update a Reactive with the properties of another Reactive:\n *   const A = reactive({ name: \"Alice\" });\n *   const B = reactive({ name: \"Bob\", age: 42 });\n *   update(A, B);\n *   console.log(`${A.name} is ${A.age}`); // \"Bob is 42\"\n *\n * @example\n *   // Update a signal with assign()-like syntax:\n *   const s = signal(42);\n *   update(s, \"hi\"); // Argument type 'string' not assignable to type 'number'\n *   update(s, {}); // Argument type '{}' not assignable to type 'number'\n *   update(s, 43);\n *   console.log(s.value); // 43\n *\n * @param obj The Reactive or Signal to be updated\n * @param update The value, Signal, object or Reactive to update `obj` to match\n * @param overwrite If `true`, any properties `obj` missing from `update` are set to `undefined`\n */\n/*\nexport function update<T extends SignalOrReactive>(\n\tobj: T,\n\tupdate: Partial<Unwrap<T>>,\n\toverwrite = false\n) {\n\tif (obj instanceof Signal) {\n\t\tobj.value = peekValue(update);\n\t} else {\n\t\tfor (let i in update) {\n\t\t\tif (i in obj) {\n\t\t\t\tobj[i].value = peekValue(update[i]);\n\t\t\t} else {\n\t\t\t\tlet sig = signal(peekValue(update[i]));\n\t\t\t\tsig[KEY] = i;\n\t\t\t\tobj[i] = sig;\n\t\t\t}\n\t\t}\n\t\tif (overwrite) {\n\t\t\tfor (let i in obj) {\n\t\t\t\tif (!(i in update)) {\n\t\t\t\t\tobj[i].value = undefined;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n*/\n"], "names": ["currentComponent", "finishUpdate", "hook", "<PERSON><PERSON><PERSON>", "hookFn", "options", "bind", "setCurrentUpdater", "updater", "_start", "SignalValue", "_ref", "_this", "this", "data", "currentSignal", "useSignal", "value", "s", "useMemo", "self", "v", "__v", "__", "__c", "_updateFlags", "wrappedSignal", "computed", "isText", "_this$base", "isValidElement", "base", "nodeType", "_updater", "_callback", "_this$base2", "peek", "setState", "effect", "oldNotify", "_notify", "notifyDomUpdates", "displayName", "Object", "defineProperties", "Signal", "prototype", "constructor", "configurable", "undefined", "type", "props", "get", "__b", "old", "vnode", "signalProps", "i", "__np", "component", "update", "createUpdater", "error", "oldVNode", "dom", "__e", "renderedProps", "updaters", "_updaters", "prop", "_dispose", "signal", "createPropUpdater", "_update", "propSignal", "setAsProperty", "ownerSVGElement", "changeSignal", "newSignal", "newProps", "setAttribute", "removeAttribute", "index", "Component", "shouldComponentUpdate", "state", "_sources", "effectsQueue", "dom<PERSON><PERSON><PERSON>", "deferEffects", "requestAnimationFrame", "setTimeout", "deferDomUpdates", "cb", "queueMicrotask", "flushEffects", "batch", "inst", "shift", "call", "notifyEffects", "push", "flushDomUpdates", "exports", "signalsCore", "untracked", "useComputed", "compute", "$compute", "useRef", "current", "useSignalEffect", "callback", "useEffect"], "mappings": "IAyCIA,EACAC,kFANJ,SAASC,EAA6BC,EAAaC,GAElDC,UAAQF,GAAYC,EAAOE,KAAK,KAAMD,EAAAA,QAAQF,IAAc,WAAO,EACpE,CAKA,SAASI,EAAkBC,GAE1B,GAAIP,EAAcA,IAElBA,EAAeO,GAAWA,EAAQC,GACnC,CAwBA,SAASC,EAAWC,GAAqD,IAAAC,EAAxBC,KAAAC,EAAIH,EAAJG,KAK1CC,EAAgBC,UAAUF,GAChCC,EAAcE,MAAQH,EAEtB,IAAMI,EAAIC,EAAOA,QAAC,WACjB,IAAIC,EAAOR,EAEPS,EAAIT,EAAKU,IACb,MAAQD,EAAIA,EAAEE,GACb,GAAIF,EAAEG,IAAK,CACVH,EAAEG,IAAIC,MAtDY,EAuDlB,KACA,CAGF,IAAMC,EAAgBC,EAAAA,SAAS,WAC9B,IACIT,EADOH,EAAcE,MACZA,MACb,OAAa,IAANC,EAAU,GAAU,IAANA,EAAa,GAAKA,GAAK,EAC7C,GAEMU,EAASD,EAAAA,SACd,WAAA,IAAAE,EAAM,OAAAC,EAAcA,eAACJ,EAAcT,QAAkC,KAAf,OAATY,EAAAjB,EAAKmB,WAAI,EAATF,EAAWG,SAAc,GAGvEpB,EAAKqB,KAAUC,EAAY,WAAKC,IAAAA,EAC/B,IAAIL,EAAAA,eAAeZ,EAAEkB,SAAmC,YAAxBD,EAAAvB,EAAKmB,aAALI,EAAWH,UAK1CpB,EAAKmB,KAAcjB,KAAOI,EAAEkB,WAL7B,CACCxB,EAAKa,MAzEkB,EA0EvBb,EAAKyB,SAAS,GAEd,CAEF,EAEAC,EAAAA,OAAO,WACN,IAAKC,EAAWA,EAAY1B,KAAK2B,EACjC3B,KAAK2B,EAAUC,EAEf,GAAIb,EAAOX,OAASG,EAAKW,KACvBX,EAAKW,KAAcjB,KAFTY,EAAcT,KAI3B,GAEA,OAAOS,CACR,EAAG,IAEH,OAAOR,EAAED,KACV,CACAP,EAAYgC,YAAc,MAE1BC,OAAOC,iBAAiBC,EAAMA,OAACC,UAAW,CACzCC,YAAa,CAAEC,cAAc,EAAM/B,WAAOgC,GAC1CC,KAAM,CAAEF,cAAc,EAAM/B,MAAOP,GACnCyC,MAAO,CACNH,cAAc,EACdI,IAAG,WACF,MAAO,CAAEtC,KAAMD,KAChB,GAKDwC,IAAK,CAAEL,cAAc,EAAM/B,MAAO,KAInCf,QAAwB,SAACoD,EAAKC,GAC7B,GAA0B,iBAAfA,EAAML,KAAmB,CACnC,IAAIM,EAEAL,EAAQI,EAAMJ,MAClB,IAAK,IAAIM,KAAKN,EACb,GAAU,aAANM,EAAJ,CAEA,IAAIxC,EAAQkC,EAAMM,GAClB,GAAIxC,aAAiB4B,EAAMA,OAAE,CAC5B,IAAKW,EAAaD,EAAMG,KAAOF,EAAc,CAAA,EAC7CA,EAAYC,GAAKxC,EACjBkC,EAAMM,GAAKxC,EAAMmB,MACjB,CALD,CAOD,CAEDkB,EAAIC,EACL,GAGArD,QAA0B,SAACoD,EAAKC,GAC/BhD,IAEA,IAAIC,EAEAmD,EAAYJ,EAAM/B,IACtB,GAAImC,EAAW,CACdA,EAAUlC,OAAgB,EAG1B,QAAgBwB,KADhBzC,EAAUmD,EAAU1B,MAEnB0B,EAAU1B,KAAWzB,EA5HxB,SAAuBoD,GACtB,IAAIpD,EACJ8B,EAAAA,OAAO,WACN9B,EAAUK,IACX,GACAL,EAAQ0B,EAuHuC,WAC5CyB,EAAUlC,MAjJa,EAkJvBkC,EAAUtB,SAAS,CAAE,EACtB,EAzHF,OAAO7B,CACR,CAqHkCqD,EAKhC,CAED7D,EAAmB2D,EACnBpD,EAAkBC,GAClB8C,EAAIC,EACL,GAGArD,EAAI,MAA2B,SAACoD,EAAKQ,EAAOP,EAAOQ,GAClDxD,IACAP,OAAmBiD,EACnBK,EAAIQ,EAAOP,EAAOQ,EACnB,GAGA7D,WAA0B,SAACoD,EAAKC,GAC/BhD,IACAP,OAAmBiD,EAEnB,IAAIe,EAIJ,GAA0B,iBAAfT,EAAML,OAAsBc,EAAMT,EAAMU,KAAiB,CACnE,IAAId,EAAQI,EAAMG,KACdQ,EAAgBX,EAAMJ,MAC1B,GAAIA,EAAO,CACV,IAAIgB,EAAWH,EAAII,EACnB,GAAID,EACH,IAAK,IAAIE,KAAQF,EAAU,CAC1B,IAAI3D,EAAU2D,EAASE,GACvB,QAAgBpB,IAAZzC,KAA2B6D,KAAQlB,GAAQ,CAC9C3C,EAAQ8D,IAERH,EAASE,QAAQpB,CACjB,CACD,MAGDe,EAAII,EADJD,EAAW,GAGZ,IAAK,IAAIE,KAAQlB,EAAO,CACvB,IAAI3C,EAAU2D,EAASE,GACnBE,EAASpB,EAAMkB,GACnB,QAAgBpB,IAAZzC,EAAuB,CAC1BA,EAAUgE,EAAkBR,EAAKK,EAAME,EAAQL,GAC/CC,EAASE,GAAQ7D,CACjB,MACAA,EAAQiE,EAAQF,EAAQL,EAEzB,CACD,CACD,CACDZ,EAAIC,EACL,GAEA,SAASiB,EACRR,EACAK,EACAK,EACAvB,GAEA,IAAMwB,EACLN,KAAQL,QAIgBf,IAAxBe,EAAIY,gBAECC,EAAeN,SAAOG,GAC5B,MAAO,CACND,EAAS,SAACK,EAAmBC,GAC5BF,EAAa5D,MAAQ6D,EACrB3B,EAAQ4B,CACT,EACAT,EAAUhC,EAAMA,OAAC,WAChB,IAAKC,EAAWA,EAAY1B,KAAK2B,EACjC3B,KAAK2B,EAAUC,EACf,IAAMxB,EAAQ4D,EAAa5D,MAAMA,MAEjC,GAAIkC,EAAMkB,KAAUpD,EAApB,CACAkC,EAAMkB,GAAQpD,EACd,GAAI0D,EAEHX,EAAIK,GAAQpD,OACFA,GAAAA,EACV+C,EAAIgB,aAAaX,EAAMpD,QAEvB+C,EAAIiB,gBAAgBZ,GAEtB,GAEF,CAGAnE,YAA2B,SAACoD,EAAKC,GAChC,GAA0B,iBAAfA,EAAML,KAAmB,CACnC,IAAIc,EAAMT,EAAMU,IAEhB,GAAID,EAAK,CACR,IAAMG,EAAWH,EAAII,EACrB,GAAID,EAAU,CACbH,EAAII,OAAYnB,EAChB,IAAK,IAAIoB,KAAQF,EAAU,CAC1B,IAAI3D,EAAU2D,EAASE,GACvB,GAAI7D,EAASA,EAAQ8D,GACrB,CACD,CACD,CACD,KAAM,CACN,IAAIX,EAAYJ,EAAM/B,IACtB,GAAImC,EAAW,CACd,IAAMnD,EAAUmD,EAAU1B,KAC1B,GAAIzB,EAAS,CACZmD,EAAU1B,UAAWgB,EACrBzC,EAAQ8D,GACR,CACD,CACD,CACDhB,EAAIC,EACL,GAGArD,EAAI,MAAoB,SAACoD,EAAKK,EAAWuB,EAAOhC,GAC/C,GAAIA,EAAO,GAAc,IAATA,EACdS,EAAiClC,MAjRb,EAkRtB6B,EAAIK,EAAWuB,EAAOhC,EACvB,GAMAiC,EAAAA,UAAUrC,UAAUsC,sBAAwB,SAE3CjC,EACAkC,GAGA,IAAM7E,EAAUK,KAAKoB,KA0BrB,KAzBmBzB,QAAgCyC,IAArBzC,EAAQ8E,GA/RjB,EAwTAzE,KAAKY,MAA+B,OAAW,EAIpE,GAAqB,EAAjBZ,KAAKY,KAAsD,OAAW,EAG1E,IAAK,IAAIgC,KAAK4B,EAAO,OAAW,EAGhC,IAAK,IAAI5B,KAAKN,EACb,GAAU,aAANM,GAAoBN,EAAMM,KAAO5C,KAAKsC,MAAMM,GAAI,OAAO,EAE5D,IAAK,IAAIA,KAAK5C,KAAKsC,MAAO,KAAMM,KAAKN,GAAQ,OAAO,EAGpD,OAAO,CACR,EAIM,SAAUnC,UAAaC,GAC5B,OAAOE,UAAQ,WAAA,OAAMoD,EAAAA,OAAsBtD,EAAM,EAAE,GACpD,CASA,IAAIsB,EACHgD,EAA8B,GAC9BC,EAA0B,GAErBC,EAC4B,oBAA1BC,sBACJC,WACAD,sBAEEE,EAAkB,SAACC,GACxBC,eAAe,WACdA,eAAeD,EAChB,EACD,EAEA,SAASE,IACRC,QAAM,WACL,IAAIC,EACJ,MAAQA,EAAOV,EAAaW,QAC3B3D,EAAU4D,KAAKF,EAEjB,EACD,CAEA,SAASG,IACR,GAAgC,IAA5Bb,EAAac,KAAKxF,OACpBR,UAAQqF,uBAAyBD,GAAcM,EAElD,CAEA,SAASO,IACRN,EAAAA,MAAM,WACL,IAAIC,EACJ,MAAQA,EAAOT,EAASU,QACvB3D,EAAU4D,KAAKF,EAEjB,EACD,CAEA,SAASxD,IACR,GAA4B,IAAxB+C,EAASa,KAAKxF,OAChBR,EAAOA,QAACqF,uBAAyBE,GAAiBU,EAErD,CAaAC,QAAA1D,OAAA2D,EAAA3D,OAAA0D,QAAAP,MAAAQ,EAAAR,MAAAO,QAAA5E,SAAA6E,EAAA7E,SAAA4E,QAAAjE,OAAAkE,EAAAlE,OAAAiE,QAAAhC,OAAAiC,EAAAjC,OAAAgC,QAAAE,UAAAD,EAAAC,UAAAF,QAAAG,YA/DM,SAAyBC,GAC9B,IAAMC,EAAWC,EAAAA,OAAOF,GACxBC,EAASE,QAAUH,EAClB3G,EAAwCyB,MApVpB,EAqVrB,OAAON,EAAOA,QAAC,kBAAMQ,WAAY,WAAM,OAAAiF,EAASE,SAAS,EAAC,EAAE,GAC7D,EA0DAP,QAAAvF,UAAAA,UAAAuF,QAAAQ,gBAXgB,SAAgBlB,GAC/B,IAAMmB,EAAWH,EAAMA,OAAChB,GACxBmB,EAASF,QAAUjB,EAEnBoB,YAAU,WACT,OAAO3E,EAAAA,OAAO,WACb,IAAKC,EAAWA,EAAY1B,KAAK2B,EACjC3B,KAAK2B,EAAU4D,EACf,OAAOY,EAASF,SACjB,EACD,EAAG,GACJ"}