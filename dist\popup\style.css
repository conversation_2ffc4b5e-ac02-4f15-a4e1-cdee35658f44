html, body {
  height: 100%; /* 确保 html 和 body 占据全部可用高度 */
  margin: 0;
  padding: 0; /* 重置 padding */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  width: 400px; /* 接近请求的宽度 */
  overflow-x: hidden;
}

body { /* 单独为 body 设置，避免覆盖 html 的 width */
  height: 600px;
}

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 550px; /* 尝试达到接近600px的总高度 */
  padding: 15px;
  box-sizing: border-box;
  background-color: #f9f9f9;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px; /* 增加与卡片区域的间距 */
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 48px; /* 增大头像 */
  height: 48px;
  border-radius: 50%;
  margin-right: 12px; /* 增大间距 */
  border: 1px solid #ddd;
}

.login-status {
  font-size: 1.3em; /* 增大字体 */
  font-weight: bold;
}

.upgrade-button {
  background-color: #333;
  color: #ffd700; /* 金黄色 */
  border: none;
  padding: 10px 18px; /* 增大内边距 */
  border-radius: 20px;
  cursor: pointer;
  font-size: 1.1em; /* 增大字体 */
  display: flex;
  align-items: center;
  gap: 5px;
}
.upgrade-button:hover {
  background-color: #555;
}
.upgrade-button span[role="img"] {
    font-size: 1.2em;
}


/* Card Grid */
.card-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px; /* 卡片间距 */
  flex-grow: 1; /* 让卡片区域占据剩余空间 */
  margin-bottom: 25px; /* 增加与底部的间距 */
}

.card {
  background-color: white;
  border-radius: 12px; /* 圆角 */
  padding: 15px;
  text-align: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100px; /* 给卡片一个最小高度 */
  transition: transform 0.2s ease-in-out;
}
.card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 2.8em; /* 增大图标 */
  margin-bottom: 10px;
}

.card-text {
  font-size: 0.95em;
  color: #333;
}

/* Footer */
.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #e0e0e0;
}

.settings {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.settings-icon {
  font-size: 1.8em; /* 增大设置图标 */
  margin-right: 8px;
}

.settings-text {
  font-size: 1.2em; /* 增大设置文字 */
  color: #555;
}
.settings:hover .settings-text {
    color: #000;
}

.version {
  font-size: 1em;
  color: #000000; /* 黑色版本号 */
}