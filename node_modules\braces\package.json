{"name": "braces", "description": "Fastest brace expansion for node.js, with the most complete support for the Bash 4.3 braces specification.", "version": "1.8.5", "homepage": "https://github.com/jonschlinkert/braces", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}, "devDependencies": {"benchmarked": "^0.1.5", "brace-expansion": "^1.1.3", "chalk": "^1.1.3", "gulp-format-md": "^0.1.8", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "^2.4.5", "should": "^8.3.1"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "verb": {"plugins": ["gulp-format-md"], "reflinks": ["verb"], "toc": false, "layout": "default", "lint": {"reflinks": true}, "tasks": ["readme"], "related": {"list": ["micromatch", "expand-range", "fill-range"]}}}