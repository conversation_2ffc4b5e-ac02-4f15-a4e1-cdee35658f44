{"name": "@preact/signals", "version": "2.0.0", "license": "MIT", "description": "Manage state with style in Preact", "keywords": [], "authors": ["The Preact Authors (https://github.com/preactjs/signals/contributors)"], "repository": {"type": "git", "url": "https://github.com/preactjs/signals", "directory": "packages/preact"}, "bugs": "https://github.com/preactjs/signals/issues", "homepage": "https://preactjs.com", "funding": {"type": "opencollective", "url": "https://opencollective.com/preact"}, "amdName": "preactSignals", "main": "dist/signals.js", "module": "dist/signals.module.js", "unpkg": "dist/signals.min.js", "types": "dist/signals.d.ts", "source": "src/index.ts", "exports": {".": {"types": "./dist/signals.d.ts", "browser": "./dist/signals.module.js", "import": "./dist/signals.mjs", "require": "./dist/signals.js"}}, "mangle": "../../mangle.json", "files": ["src", "dist", "CHANGELOG.md", "LICENSE", "README.md"], "dependencies": {"@preact/signals-core": "^1.7.0"}, "peerDependencies": {"preact": "10.x"}, "devDependencies": {"preact": "10.9.0", "preact-render-to-string": "^5.2.5"}, "publishConfig": {"provenance": true}, "scripts": {}}