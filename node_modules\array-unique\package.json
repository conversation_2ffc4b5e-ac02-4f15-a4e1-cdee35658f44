{"name": "array-unique", "description": "Return an array free of duplicate values. Fastest ES5 implementation.", "version": "0.2.1", "homepage": "https://github.com/jonschlinkert/array-unique", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/array-unique.git"}, "bugs": {"url": "https://github.com/jonschlinkert/array-unique/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/array-unique/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"array-uniq": "^1.0.2", "benchmarked": "^0.1.3", "mocha": "*", "should": "*"}}