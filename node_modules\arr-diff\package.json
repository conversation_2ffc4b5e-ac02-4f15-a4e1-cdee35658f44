{"name": "arr-diff", "description": "Returns an array with only the unique values from the first array, by excluding all values from additional arrays using strict equality for comparisons.", "version": "2.0.0", "homepage": "https://github.com/jonschlinkert/arr-diff", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/arr-diff", "bugs": {"url": "https://github.com/jonschlinkert/arr-diff/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"arr-flatten": "^1.0.1"}, "devDependencies": {"array-differ": "^1.0.0", "array-slice": "^0.2.3", "benchmarked": "^0.1.4", "chalk": "^1.1.1", "mocha": "*", "should": "*"}, "keywords": ["arr", "array", "diff", "differ", "difference"], "verb": {"related": {"list": ["arr-flatten", "array-filter", "array-intersection"]}}}