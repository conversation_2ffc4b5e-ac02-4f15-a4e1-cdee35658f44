"use strict";
(() => {
  // src/background.ts
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "openNewExtensionTab") {
      chrome.tabs.create({ url: chrome.runtime.getURL("new_tab_page.html") });
      return true;
    }
  });
  chrome.runtime.onInstalled.addListener(() => {
    console.log("\u6211\u7684 Preact \u6269\u5C55\u5DF2\u5B89\u88C5\u6216\u66F4\u65B0\u3002");
  });
})();
