import{Component as t,options as i,isValidElement as e}from"preact";import{useMemo as n,useRef as o,useEffect as f}from"preact/hooks";import{Signal as r,computed as s,effect as u,signal as c,batch as l}from"@preact/signals-core";export{Signal,batch,computed,effect,signal,untracked}from"@preact/signals-core";function a(t,e){i[t]=e.bind(null,i[t]||(()=>{}))}let h,d;function p(t){if(d)d();d=t&&t.S()}function v({data:t}){const i=useSignal(t);i.value=t;const o=n(()=>{let t=this,n=this.__v;while(n=n.__)if(n.__c){n.__c.__$f|=4;break}const f=s(function(){let t=i.value.value;return 0===t?0:!0===t?"":t||""}),r=s(()=>{var t;return e(f.value)||3!==(null==(t=this.base)?void 0:t.nodeType)});this.__$u.c=()=>{var t;if(!e(o.peek())&&3===(null==(t=this.base)?void 0:t.nodeType))this.base.data=o.peek();else{this.__$f|=1;this.setState({})}};u(function(){if(!m)m=this.N;this.N=A;if(r.value&&t.base)t.base.data=f.value});return f},[]);return o.value}v.displayName="_st";Object.defineProperties(r.prototype,{constructor:{configurable:!0,value:void 0},type:{configurable:!0,value:v},props:{configurable:!0,get(){return{data:this}}},__b:{configurable:!0,value:1}});a("__b",(t,i)=>{if("string"==typeof i.type){let t,e=i.props;for(let n in e){if("children"===n)continue;let o=e[n];if(o instanceof r){if(!t)i.__np=t={};t[n]=o;e[n]=o.peek()}}}t(i)});a("__r",(t,i)=>{p();let e,n=i.__c;if(n){n.__$f&=-2;e=n.__$u;if(void 0===e)n.__$u=e=function(t){let i;u(function(){i=this});i.c=()=>{n.__$f|=1;n.setState({})};return i}()}h=n;p(e);t(i)});a("__e",(t,i,e,n)=>{p();h=void 0;t(i,e,n)});a("diffed",(t,i)=>{p();h=void 0;let e;if("string"==typeof i.type&&(e=i.__e)){let t=i.__np,n=i.props;if(t){let i=e.U;if(i)for(let e in i){let n=i[e];if(void 0!==n&&!(e in t)){n.d();i[e]=void 0}}else{i={};e.U=i}for(let o in t){let f=i[o],r=t[o];if(void 0===f){f=_(e,o,r,n);i[o]=f}else f.o(r,n)}}}t(i)});function _(t,i,e,n){const o=i in t&&void 0===t.ownerSVGElement,f=c(e);return{o:(t,i)=>{f.value=t;n=i},d:u(function(){if(!m)m=this.N;this.N=A;const e=f.value.value;if(n[i]!==e){n[i]=e;if(o)t[i]=e;else if(e)t.setAttribute(i,e);else t.removeAttribute(i)}})}}a("unmount",(t,i)=>{if("string"==typeof i.type){let t=i.__e;if(t){const i=t.U;if(i){t.U=void 0;for(let t in i){let e=i[t];if(e)e.d()}}}}else{let t=i.__c;if(t){const i=t.__$u;if(i){t.__$u=void 0;i.d()}}}t(i)});a("__h",(t,i,e,n)=>{if(n<3||9===n)i.__$f|=2;t(i,e,n)});t.prototype.shouldComponentUpdate=function(t,i){const e=this.__$u;if(!(e&&void 0!==e.s||4&this.__$f))return!0;if(3&this.__$f)return!0;for(let t in i)return!0;for(let i in t)if("__source"!==i&&t[i]!==this.props[i])return!0;for(let i in this.props)if(!(i in t))return!0;return!1};function useSignal(t){return n(()=>c(t),[])}function useComputed(t){const i=o(t);i.current=t;h.__$f|=4;return n(()=>s(()=>i.current()),[])}let m,g=[],b=[];const k="undefined"==typeof requestAnimationFrame?setTimeout:requestAnimationFrame,y=t=>{queueMicrotask(()=>{queueMicrotask(t)})};function q(){l(()=>{let t;while(t=g.shift())m.call(t)})}function w(){if(1===g.push(this))(i.requestAnimationFrame||k)(q)}function x(){l(()=>{let t;while(t=b.shift())m.call(t)})}function A(){if(1===b.push(this))(i.requestAnimationFrame||y)(x)}function useSignalEffect(t){const i=o(t);i.current=t;f(()=>u(function(){if(!m)m=this.N;this.N=w;return i.current()}),[])}export{useComputed,useSignal,useSignalEffect};//# sourceMappingURL=signals.mjs.map
