{"version": 3, "file": "signals.mjs", "sources": ["../src/index.ts"], "sourcesContent": ["import { options, Component, isValidElement } from \"preact\";\nimport { useRef, useMemo, useEffect } from \"preact/hooks\";\nimport {\n\tsignal,\n\tcomputed,\n\tbatch,\n\teffect,\n\tSignal,\n\ttype ReadonlySignal,\n\tuntracked,\n} from \"@preact/signals-core\";\nimport {\n\tVNode,\n\tOptionsTypes,\n\tHookFn,\n\tEffect,\n\tPropertyUpdater,\n\tAugmentedComponent,\n\tAugmentedElement as Element,\n} from \"./internal\";\n\nexport {\n\tsignal,\n\tcomputed,\n\tbatch,\n\teffect,\n\tSignal,\n\ttype ReadonlySignal,\n\tuntracked,\n};\n\nconst HAS_PENDING_UPDATE = 1 << 0;\nconst HAS_HOOK_STATE = 1 << 1;\nconst HAS_COMPUTEDS = 1 << 2;\n\n// Install a Preact options hook\nfunction hook<T extends OptionsTypes>(hookName: T, hookFn: HookFn<T>) {\n\t// @ts-ignore-next-line private options hooks usage\n\toptions[hookName] = hookFn.bind(null, options[hookName] || (() => {}));\n}\n\nlet currentComponent: AugmentedComponent | undefined;\nlet finishUpdate: (() => void) | undefined;\n\nfunction setCurrentUpdater(updater?: Effect) {\n\t// end tracking for the current update:\n\tif (finishUpdate) finishUpdate();\n\t// start tracking the new update:\n\tfinishUpdate = updater && updater._start();\n}\n\nfunction createUpdater(update: () => void) {\n\tlet updater!: Effect;\n\teffect(function (this: Effect) {\n\t\tupdater = this;\n\t});\n\tupdater._callback = update;\n\treturn updater;\n}\n\n/** @todo This may be needed for complex prop value detection. */\n// function isSignalValue(value: any): value is Signal {\n// \tif (typeof value !== \"object\" || value == null) return false;\n// \tif (value instanceof Signal) return true;\n// \t// @TODO: uncomment this when we land Reactive (ideally behind a brand check)\n// \t// for (let i in value) if (value[i] instanceof Signal) return true;\n// \treturn false;\n// }\n\n/**\n * A wrapper component that renders a Signal directly as a Text node.\n * @todo: in Preact 11, just decorate Signal with `type:null`\n */\nfunction SignalValue(this: AugmentedComponent, { data }: { data: Signal }) {\n\t// hasComputeds.add(this);\n\n\t// Store the props.data signal in another signal so that\n\t// passing a new signal reference re-runs the text computed:\n\tconst currentSignal = useSignal(data);\n\tcurrentSignal.value = data;\n\n\tconst s = useMemo(() => {\n\t\tlet self = this;\n\t\t// mark the parent component as having computeds so it gets optimized\n\t\tlet v = this.__v;\n\t\twhile ((v = v.__!)) {\n\t\t\tif (v.__c) {\n\t\t\t\tv.__c._updateFlags |= HAS_COMPUTEDS;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tconst wrappedSignal = computed(function (this: Effect) {\n\t\t\tlet data = currentSignal.value;\n\t\t\tlet s = data.value;\n\t\t\treturn s === 0 ? 0 : s === true ? \"\" : s || \"\";\n\t\t});\n\n\t\tconst isText = computed(\n\t\t\t() => isValidElement(wrappedSignal.value) || this.base?.nodeType !== 3\n\t\t);\n\n\t\tthis._updater!._callback = () => {\n\t\t\tif (isValidElement(s.peek()) || this.base?.nodeType !== 3) {\n\t\t\t\tthis._updateFlags |= HAS_PENDING_UPDATE;\n\t\t\t\tthis.setState({});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t(this.base as Text).data = s.peek();\n\t\t};\n\n\t\teffect(function (this: Effect) {\n\t\t\tif (!oldNotify) oldNotify = this._notify;\n\t\t\tthis._notify = notifyDomUpdates;\n\t\t\tconst val = wrappedSignal.value;\n\t\t\tif (isText.value && self.base) {\n\t\t\t\t(self.base as Text).data = val;\n\t\t\t}\n\t\t});\n\n\t\treturn wrappedSignal;\n\t}, []);\n\n\treturn s.value;\n}\nSignalValue.displayName = \"_st\";\n\nObject.defineProperties(Signal.prototype, {\n\tconstructor: { configurable: true, value: undefined },\n\ttype: { configurable: true, value: SignalValue },\n\tprops: {\n\t\tconfigurable: true,\n\t\tget() {\n\t\t\treturn { data: this };\n\t\t},\n\t},\n\t// Setting a VNode's _depth to 1 forces Preact to clone it before modifying:\n\t// https://github.com/preactjs/preact/blob/d7a433ee8463a7dc23a05111bb47de9ec729ad4d/src/diff/children.js#L77\n\t// @todo remove this for Preact 11\n\t__b: { configurable: true, value: 1 },\n});\n\n/** Inject low-level property/attribute bindings for Signals into Preact's diff */\nhook(OptionsTypes.DIFF, (old, vnode) => {\n\tif (typeof vnode.type === \"string\") {\n\t\tlet signalProps: Record<string, any> | undefined;\n\n\t\tlet props = vnode.props;\n\t\tfor (let i in props) {\n\t\t\tif (i === \"children\") continue;\n\n\t\t\tlet value = props[i];\n\t\t\tif (value instanceof Signal) {\n\t\t\t\tif (!signalProps) vnode.__np = signalProps = {};\n\t\t\t\tsignalProps[i] = value;\n\t\t\t\tprops[i] = value.peek();\n\t\t\t}\n\t\t}\n\t}\n\n\told(vnode);\n});\n\n/** Set up Updater before rendering a component */\nhook(OptionsTypes.RENDER, (old, vnode) => {\n\tsetCurrentUpdater();\n\n\tlet updater;\n\n\tlet component = vnode.__c;\n\tif (component) {\n\t\tcomponent._updateFlags &= ~HAS_PENDING_UPDATE;\n\n\t\tupdater = component._updater;\n\t\tif (updater === undefined) {\n\t\t\tcomponent._updater = updater = createUpdater(() => {\n\t\t\t\tcomponent._updateFlags |= HAS_PENDING_UPDATE;\n\t\t\t\tcomponent.setState({});\n\t\t\t});\n\t\t}\n\t}\n\n\tcurrentComponent = component;\n\tsetCurrentUpdater(updater);\n\told(vnode);\n});\n\n/** Finish current updater if a component errors */\nhook(OptionsTypes.CATCH_ERROR, (old, error, vnode, oldVNode) => {\n\tsetCurrentUpdater();\n\tcurrentComponent = undefined;\n\told(error, vnode, oldVNode);\n});\n\n/** Finish current updater after rendering any VNode */\nhook(OptionsTypes.DIFFED, (old, vnode) => {\n\tsetCurrentUpdater();\n\tcurrentComponent = undefined;\n\n\tlet dom: Element;\n\n\t// vnode._dom is undefined during string rendering,\n\t// so we use this to skip prop subscriptions during SSR.\n\tif (typeof vnode.type === \"string\" && (dom = vnode.__e as Element)) {\n\t\tlet props = vnode.__np;\n\t\tlet renderedProps = vnode.props;\n\t\tif (props) {\n\t\t\tlet updaters = dom._updaters;\n\t\t\tif (updaters) {\n\t\t\t\tfor (let prop in updaters) {\n\t\t\t\t\tlet updater = updaters[prop];\n\t\t\t\t\tif (updater !== undefined && !(prop in props)) {\n\t\t\t\t\t\tupdater._dispose();\n\t\t\t\t\t\t// @todo we could just always invoke _dispose() here\n\t\t\t\t\t\tupdaters[prop] = undefined;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tupdaters = {};\n\t\t\t\tdom._updaters = updaters;\n\t\t\t}\n\t\t\tfor (let prop in props) {\n\t\t\t\tlet updater = updaters[prop];\n\t\t\t\tlet signal = props[prop];\n\t\t\t\tif (updater === undefined) {\n\t\t\t\t\tupdater = createPropUpdater(dom, prop, signal, renderedProps);\n\t\t\t\t\tupdaters[prop] = updater;\n\t\t\t\t} else {\n\t\t\t\t\tupdater._update(signal, renderedProps);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\told(vnode);\n});\n\nfunction createPropUpdater(\n\tdom: Element,\n\tprop: string,\n\tpropSignal: Signal,\n\tprops: Record<string, any>\n): PropertyUpdater {\n\tconst setAsProperty =\n\t\tprop in dom &&\n\t\t// SVG elements need to go through `setAttribute` because they\n\t\t// expect things like SVGAnimatedTransformList instead of strings.\n\t\t// @ts-ignore\n\t\tdom.ownerSVGElement === undefined;\n\n\tconst changeSignal = signal(propSignal);\n\treturn {\n\t\t_update: (newSignal: Signal, newProps: typeof props) => {\n\t\t\tchangeSignal.value = newSignal;\n\t\t\tprops = newProps;\n\t\t},\n\t\t_dispose: effect(function (this: Effect) {\n\t\t\tif (!oldNotify) oldNotify = this._notify;\n\t\t\tthis._notify = notifyDomUpdates;\n\t\t\tconst value = changeSignal.value.value;\n\t\t\t// If Preact just rendered this value, don't render it again:\n\t\t\tif (props[prop] === value) return;\n\t\t\tprops[prop] = value;\n\t\t\tif (setAsProperty) {\n\t\t\t\t// @ts-ignore-next-line silly\n\t\t\t\tdom[prop] = value;\n\t\t\t} else if (value) {\n\t\t\t\tdom.setAttribute(prop, value);\n\t\t\t} else {\n\t\t\t\tdom.removeAttribute(prop);\n\t\t\t}\n\t\t}),\n\t};\n}\n\n/** Unsubscribe from Signals when unmounting components/vnodes */\nhook(OptionsTypes.UNMOUNT, (old, vnode: VNode) => {\n\tif (typeof vnode.type === \"string\") {\n\t\tlet dom = vnode.__e as Element | undefined;\n\t\t// vnode._dom is undefined during string rendering\n\t\tif (dom) {\n\t\t\tconst updaters = dom._updaters;\n\t\t\tif (updaters) {\n\t\t\t\tdom._updaters = undefined;\n\t\t\t\tfor (let prop in updaters) {\n\t\t\t\t\tlet updater = updaters[prop];\n\t\t\t\t\tif (updater) updater._dispose();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t} else {\n\t\tlet component = vnode.__c;\n\t\tif (component) {\n\t\t\tconst updater = component._updater;\n\t\t\tif (updater) {\n\t\t\t\tcomponent._updater = undefined;\n\t\t\t\tupdater._dispose();\n\t\t\t}\n\t\t}\n\t}\n\told(vnode);\n});\n\n/** Mark components that use hook state so we can skip sCU optimization. */\nhook(OptionsTypes.HOOK, (old, component, index, type) => {\n\tif (type < 3 || type === 9)\n\t\t(component as AugmentedComponent)._updateFlags |= HAS_HOOK_STATE;\n\told(component, index, type);\n});\n\n/**\n * Auto-memoize components that use Signals/Computeds.\n * Note: Does _not_ optimize components that use hook/class state.\n */\nComponent.prototype.shouldComponentUpdate = function (\n\tthis: AugmentedComponent,\n\tprops,\n\tstate\n) {\n\t// @todo: Once preactjs/preact#3671 lands, this could just use `currentUpdater`:\n\tconst updater = this._updater;\n\tconst hasSignals = updater && updater._sources !== undefined;\n\n\t// let reason;\n\t// if (!hasSignals && !hasComputeds.has(this)) {\n\t// \treason = \"no signals or computeds\";\n\t// } else if (hasPendingUpdate.has(this)) {\n\t// \treason = \"has pending update\";\n\t// } else if (hasHookState.has(this)) {\n\t// \treason = \"has hook state\";\n\t// }\n\t// if (reason) {\n\t// \tif (!this) reason += \" (`this` bug)\";\n\t// \tconsole.log(\"not optimizing\", this?.constructor?.name, \": \", reason, {\n\t// \t\tdetails: {\n\t// \t\t\thasSignals,\n\t// \t\t\thasComputeds: hasComputeds.has(this),\n\t// \t\t\thasPendingUpdate: hasPendingUpdate.has(this),\n\t// \t\t\thasHookState: hasHookState.has(this),\n\t// \t\t\tdeps: Array.from(updater._deps),\n\t// \t\t\tupdater,\n\t// \t\t},\n\t// \t});\n\t// }\n\n\t// if this component used no signals or computeds, update:\n\tif (!hasSignals && !(this._updateFlags & HAS_COMPUTEDS)) return true;\n\n\t// if there is a pending re-render triggered from Signals,\n\t// or if there is hook or class state, update:\n\tif (this._updateFlags & (HAS_PENDING_UPDATE | HAS_HOOK_STATE)) return true;\n\n\t// @ts-ignore\n\tfor (let i in state) return true;\n\n\t// if any non-Signal props changed, update:\n\tfor (let i in props) {\n\t\tif (i !== \"__source\" && props[i] !== this.props[i]) return true;\n\t}\n\tfor (let i in this.props) if (!(i in props)) return true;\n\n\t// this is a purely Signal-driven component, don't update:\n\treturn false;\n};\n\nexport function useSignal<T>(value: T): Signal<T>;\nexport function useSignal<T = undefined>(): Signal<T | undefined>;\nexport function useSignal<T>(value?: T) {\n\treturn useMemo(() => signal<T | undefined>(value), []);\n}\n\nexport function useComputed<T>(compute: () => T) {\n\tconst $compute = useRef(compute);\n\t$compute.current = compute;\n\t(currentComponent as AugmentedComponent)._updateFlags |= HAS_COMPUTEDS;\n\treturn useMemo(() => computed<T>(() => $compute.current()), []);\n}\n\nlet oldNotify: (this: Effect) => void,\n\teffectsQueue: Array<Effect> = [],\n\tdomQueue: Array<Effect> = [];\n\nconst deferEffects =\n\ttypeof requestAnimationFrame === \"undefined\"\n\t\t? setTimeout\n\t\t: requestAnimationFrame;\n\nconst deferDomUpdates = (cb: any) => {\n\tqueueMicrotask(() => {\n\t\tqueueMicrotask(cb);\n\t});\n};\n\nfunction flushEffects() {\n\tbatch(() => {\n\t\tlet inst: Effect | undefined;\n\t\twhile ((inst = effectsQueue.shift())) {\n\t\t\toldNotify.call(inst);\n\t\t}\n\t});\n}\n\nfunction notifyEffects(this: Effect) {\n\tif (effectsQueue.push(this) === 1) {\n\t\t(options.requestAnimationFrame || deferEffects)(flushEffects);\n\t}\n}\n\nfunction flushDomUpdates() {\n\tbatch(() => {\n\t\tlet inst: Effect | undefined;\n\t\twhile ((inst = domQueue.shift())) {\n\t\t\toldNotify.call(inst);\n\t\t}\n\t});\n}\n\nfunction notifyDomUpdates(this: Effect) {\n\tif (domQueue.push(this) === 1) {\n\t\t(options.requestAnimationFrame || deferDomUpdates)(flushDomUpdates);\n\t}\n}\n\nexport function useSignalEffect(cb: () => void | (() => void)) {\n\tconst callback = useRef(cb);\n\tcallback.current = cb;\n\n\tuseEffect(() => {\n\t\treturn effect(function (this: Effect) {\n\t\t\tif (!oldNotify) oldNotify = this._notify;\n\t\t\tthis._notify = notifyEffects;\n\t\t\treturn callback.current();\n\t\t});\n\t}, []);\n}\n\n/**\n * @todo Determine which Reactive implementation we'll be using.\n * @internal\n */\n// export function useReactive<T extends object>(value: T): Reactive<T> {\n// \treturn useMemo(() => reactive<T>(value), []);\n// }\n\n/**\n * @internal\n * Update a Reactive's using the properties of an object or other Reactive.\n * Also works for Signals.\n * @example\n *   // Update a Reactive with Object.assign()-like syntax:\n *   const r = reactive({ name: \"Alice\" });\n *   update(r, { name: \"Bob\" });\n *   update(r, { age: 42 }); // property 'age' does not exist in type '{ name?: string }'\n *   update(r, 2); // '2' has no properties in common with '{ name?: string }'\n *   console.log(r.name.value); // \"Bob\"\n *\n * @example\n *   // Update a Reactive with the properties of another Reactive:\n *   const A = reactive({ name: \"Alice\" });\n *   const B = reactive({ name: \"Bob\", age: 42 });\n *   update(A, B);\n *   console.log(`${A.name} is ${A.age}`); // \"Bob is 42\"\n *\n * @example\n *   // Update a signal with assign()-like syntax:\n *   const s = signal(42);\n *   update(s, \"hi\"); // Argument type 'string' not assignable to type 'number'\n *   update(s, {}); // Argument type '{}' not assignable to type 'number'\n *   update(s, 43);\n *   console.log(s.value); // 43\n *\n * @param obj The Reactive or Signal to be updated\n * @param update The value, Signal, object or Reactive to update `obj` to match\n * @param overwrite If `true`, any properties `obj` missing from `update` are set to `undefined`\n */\n/*\nexport function update<T extends SignalOrReactive>(\n\tobj: T,\n\tupdate: Partial<Unwrap<T>>,\n\toverwrite = false\n) {\n\tif (obj instanceof Signal) {\n\t\tobj.value = peekValue(update);\n\t} else {\n\t\tfor (let i in update) {\n\t\t\tif (i in obj) {\n\t\t\t\tobj[i].value = peekValue(update[i]);\n\t\t\t} else {\n\t\t\t\tlet sig = signal(peekValue(update[i]));\n\t\t\t\tsig[KEY] = i;\n\t\t\t\tobj[i] = sig;\n\t\t\t}\n\t\t}\n\t\tif (overwrite) {\n\t\t\tfor (let i in obj) {\n\t\t\t\tif (!(i in update)) {\n\t\t\t\t\tobj[i].value = undefined;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n*/\n"], "names": ["Component", "options", "isValidElement", "useMemo", "useRef", "useEffect", "Signal", "computed", "effect", "signal", "batch", "untracked", "hook", "<PERSON><PERSON><PERSON>", "hookFn", "bind", "currentComponent", "finishUpdate", "setCurrentUpdater", "updater", "_start", "SignalValue", "data", "currentSignal", "useSignal", "value", "s", "self", "this", "v", "__v", "__", "__c", "_updateFlags", "wrappedSignal", "isText", "_this$base", "base", "nodeType", "_updater", "_callback", "_this$base2", "peek", "setState", "oldNotify", "_notify", "notifyDomUpdates", "displayName", "Object", "defineProperties", "prototype", "constructor", "configurable", "undefined", "type", "props", "get", "__b", "old", "vnode", "signalProps", "i", "__np", "component", "update", "createUpdater", "error", "oldVNode", "dom", "__e", "renderedProps", "updaters", "_updaters", "prop", "_dispose", "createPropUpdater", "_update", "propSignal", "setAsProperty", "ownerSVGElement", "changeSignal", "newSignal", "newProps", "setAttribute", "removeAttribute", "index", "shouldComponentUpdate", "state", "_sources", "useComputed", "compute", "$compute", "current", "effectsQueue", "dom<PERSON><PERSON><PERSON>", "deferEffects", "requestAnimationFrame", "setTimeout", "deferDomUpdates", "cb", "queueMicrotask", "flushEffects", "inst", "shift", "call", "notifyEffects", "push", "flushDomUpdates", "useSignalEffect", "callback"], "mappings": "oBA+BAA,aAAAC,oBAAAC,MAAA,2BAAAC,YAAAC,eAAAC,MAAA,gCAAAC,cAAAC,YAAAC,YAAAC,WAAAC,MAAA,8BAAAJ,OAAAI,MAAAH,SAAAC,OAAAC,OAAAE,cAAA,uBAKA,SAASC,EAA6BC,EAAaC,GAElDb,EAAQY,GAAYC,EAAOC,KAAK,KAAMd,EAAQY,IAAc,MAAQ,GACrE,CAEA,IAAIG,EACAC,EAEJ,SAASC,EAAkBC,GAE1B,GAAIF,EAAcA,IAElBA,EAAeE,GAAWA,EAAQC,GACnC,CAwBA,SAASC,GAAsCC,KAAEA,IAKhD,MAAMC,EAAgBC,UAAUF,GAChCC,EAAcE,MAAQH,EAEtB,MAAMI,EAAIvB,EAAQ,KACjB,IAAIwB,EAAOC,KAEPC,EAAID,KAAKE,IACb,MAAQD,EAAIA,EAAEE,GACb,GAAIF,EAAEG,IAAK,CACVH,EAAEG,IAAIC,MAtDY,EAuDlB,KACA,CAGF,MAAMC,EAAgB3B,EAAS,WAC9B,IACImB,EADOH,EAAcE,MACZA,MACb,OAAa,IAANC,EAAU,GAAU,IAANA,EAAa,GAAKA,GAAK,EAC7C,GAEMS,EAAS5B,EACd,KAAA,IAAA6B,EAAM,OAAAlC,EAAegC,EAAcT,QAAkC,KAAf,OAATW,EAAAR,KAAKS,WAAI,EAATD,EAAWE,SAAa,GAGtEV,KAAKW,KAAUC,EAAY,KAAKC,IAAAA,EAC/B,IAAIvC,EAAewB,EAAEgB,SAAmC,KAAxBD,OAAAA,OAAKJ,WAALI,EAAAA,EAAWH,UAK1CV,KAAKS,KAAcf,KAAOI,EAAEgB,WAL7B,CACCd,KAAKK,MAzEkB,EA0EvBL,KAAKe,SAAS,CAAA,EAEd,CAEF,EAEAnC,EAAO,WACN,IAAKoC,EAAWA,EAAYhB,KAAKiB,EACjCjB,KAAKiB,EAAUC,EAEf,GAAIX,EAAOV,OAASE,EAAKU,KACvBV,EAAKU,KAAcf,KAFTY,EAAcT,KAI3B,GAEA,OAAOS,GACL,IAEH,OAAOR,EAAED,KACV,CACAJ,EAAY0B,YAAc,MAE1BC,OAAOC,iBAAiB3C,EAAO4C,UAAW,CACzCC,YAAa,CAAEC,cAAc,EAAM3B,WAAO4B,GAC1CC,KAAM,CAAEF,cAAc,EAAM3B,MAAOJ,GACnCkC,MAAO,CACNH,cAAc,EACdI,MACC,MAAO,CAAElC,KAAMM,KAChB,GAKD6B,IAAK,CAAEL,cAAc,EAAM3B,MAAO,KAInCb,QAAwB,CAAC8C,EAAKC,KAC7B,GAA0B,iBAAfA,EAAML,KAAmB,CACnC,IAAIM,EAEAL,EAAQI,EAAMJ,MAClB,IAAK,IAAIM,KAAKN,EAAO,CACpB,GAAU,aAANM,EAAkB,SAEtB,IAAIpC,EAAQ8B,EAAMM,GAClB,GAAIpC,aAAiBnB,EAAQ,CAC5B,IAAKsD,EAAaD,EAAMG,KAAOF,EAAc,CAAE,EAC/CA,EAAYC,GAAKpC,EACjB8B,EAAMM,GAAKpC,EAAMiB,MACjB,CACD,CACD,CAEDgB,EAAIC,EACL,GAGA/C,QAA0B,CAAC8C,EAAKC,KAC/BzC,IAEA,IAAIC,EAEA4C,EAAYJ,EAAM3B,IACtB,GAAI+B,EAAW,CACdA,EAAU9B,OAAgB,EAE1Bd,EAAU4C,EAAUxB,KACpB,QAAgBc,IAAZlC,EACH4C,EAAUxB,KAAWpB,EA5HxB,SAAuB6C,GACtB,IAAI7C,EACJX,EAAO,WACNW,EAAUS,IACX,GACAT,EAAQqB,EAuHuC,KAC5CuB,EAAU9B,MAjJa,EAkJvB8B,EAAUpB,SAAS,CAAA,IAxHtB,OAAOxB,CACR,CAqHkC8C,EAKhC,CAEDjD,EAAmB+C,EACnB7C,EAAkBC,GAClBuC,EAAIC,EAAK,GAIV/C,EAAI,MAA2B,CAAC8C,EAAKQ,EAAOP,EAAOQ,KAClDjD,IACAF,OAAmBqC,EACnBK,EAAIQ,EAAOP,EAAOQ,EACnB,GAGAvD,WAA0B,CAAC8C,EAAKC,KAC/BzC,IACAF,OAAmBqC,EAEnB,IAAIe,EAIJ,GAA0B,iBAAfT,EAAML,OAAsBc,EAAMT,EAAMU,KAAiB,CACnE,IAAId,EAAQI,EAAMG,KACdQ,EAAgBX,EAAMJ,MAC1B,GAAIA,EAAO,CACV,IAAIgB,EAAWH,EAAII,EACnB,GAAID,EACH,IAAK,IAAIE,KAAQF,EAAU,CAC1B,IAAIpD,EAAUoD,EAASE,GACvB,QAAgBpB,IAAZlC,KAA2BsD,KAAQlB,GAAQ,CAC9CpC,EAAQuD,IAERH,EAASE,QAAQpB,CACjB,CACD,KACK,CACNkB,EAAW,CAAA,EACXH,EAAII,EAAYD,CAChB,CACD,IAAK,IAAIE,KAAQlB,EAAO,CACvB,IAAIpC,EAAUoD,EAASE,GACnBhE,EAAS8C,EAAMkB,GACnB,QAAgBpB,IAAZlC,EAAuB,CAC1BA,EAAUwD,EAAkBP,EAAKK,EAAMhE,EAAQ6D,GAC/CC,EAASE,GAAQtD,CACjB,MACAA,EAAQyD,EAAQnE,EAAQ6D,EAEzB,CACD,CACD,CACDZ,EAAIC,EACL,GAEA,SAASgB,EACRP,EACAK,EACAI,EACAtB,GAEA,MAAMuB,EACLL,KAAQL,QAIgBf,IAAxBe,EAAIW,gBAECC,EAAevE,EAAOoE,GAC5B,MAAO,CACND,EAASA,CAACK,EAAmBC,KAC5BF,EAAavD,MAAQwD,EACrB1B,EAAQ2B,CAAAA,EAETR,EAAUlE,EAAO,WAChB,IAAKoC,EAAWA,EAAYhB,KAAKiB,EACjCjB,KAAKiB,EAAUC,EACf,MAAMrB,EAAQuD,EAAavD,MAAMA,MAEjC,GAAI8B,EAAMkB,KAAUhD,EAApB,CACA8B,EAAMkB,GAAQhD,EACd,GAAIqD,EAEHV,EAAIK,GAAQhD,OACN,GAAIA,EACV2C,EAAIe,aAAaV,EAAMhD,QAEvB2C,EAAIgB,gBAAgBX,EARM,CAU5B,GAEF,CAGA7D,YAA2B,CAAC8C,EAAKC,KAChC,GAA0B,iBAAfA,EAAML,KAAmB,CACnC,IAAIc,EAAMT,EAAMU,IAEhB,GAAID,EAAK,CACR,MAAMG,EAAWH,EAAII,EACrB,GAAID,EAAU,CACbH,EAAII,OAAYnB,EAChB,IAAK,IAAIoB,KAAQF,EAAU,CAC1B,IAAIpD,EAAUoD,EAASE,GACvB,GAAItD,EAASA,EAAQuD,GACrB,CACD,CACD,CACD,KAAM,CACN,IAAIX,EAAYJ,EAAM3B,IACtB,GAAI+B,EAAW,CACd,MAAM5C,EAAU4C,EAAUxB,KAC1B,GAAIpB,EAAS,CACZ4C,EAAUxB,UAAWc,EACrBlC,EAAQuD,GACR,CACD,CACD,CACDhB,EAAIC,EAAK,GAIV/C,EAAI,MAAoB,CAAC8C,EAAKK,EAAWsB,EAAO/B,KAC/C,GAAIA,EAAO,GAAc,IAATA,EACdS,EAAiC9B,MAjRb,EAkRtByB,EAAIK,EAAWsB,EAAO/B,KAOvBtD,EAAUkD,UAAUoC,sBAAwB,SAE3C/B,EACAgC,GAGA,MAAMpE,EAAUS,KAAKW,KA0BrB,KAzBmBpB,QAAgCkC,IAArBlC,EAAQqE,GA/RjB,EAwTA5D,KAAKK,MAA+B,OAAW,EAIpE,GAAqB,EAAjBL,KAAKK,KAAsD,OAAW,EAG1E,IAAK,IAAI4B,KAAK0B,EAAO,OAAO,EAG5B,IAAK,IAAI1B,KAAKN,EACb,GAAU,aAANM,GAAoBN,EAAMM,KAAOjC,KAAK2B,MAAMM,GAAI,OAAO,EAE5D,IAAK,IAAIA,KAASjC,KAAC2B,MAAO,KAAMM,KAAKN,GAAQ,OAAW,EAGxD,OAAO,CACR,WAIgB/B,UAAaC,GAC5B,OAAOtB,EAAQ,IAAMM,EAAsBgB,GAAQ,GACpD,CAEgB,SAAAgE,YAAeC,GAC9B,MAAMC,EAAWvF,EAAOsF,GACxBC,EAASC,QAAUF,EAClB1E,EAAwCiB,MApVpB,EAqVrB,OAAO9B,EAAQ,IAAMI,EAAY,IAAMoF,EAASC,WAAY,GAC7D,CAEA,IAAIhD,EACHiD,EAA8B,GAC9BC,EAA0B,GAE3B,MAAMC,EAC4B,oBAA1BC,sBACJC,WACAD,sBAEEE,EAAmBC,IACxBC,eAAe,KACdA,eAAeD,IACf,EAGF,SAASE,IACR3F,EAAM,KACL,IAAI4F,EACJ,MAAQA,EAAOT,EAAaU,QAC3B3D,EAAU4D,KAAKF,EACf,EAEH,CAEA,SAASG,IACR,GAAgC,IAA5BZ,EAAaa,KAAK9E,OACpB3B,EAAQ+F,uBAAyBD,GAAcM,EAElD,CAEA,SAASM,IACRjG,EAAM,KACL,IAAI4F,EACJ,MAAQA,EAAOR,EAASS,QACvB3D,EAAU4D,KAAKF,EACf,EAEH,CAEA,SAASxD,IACR,GAA4B,IAAxBgD,EAASY,KAAK9E,OAChB3B,EAAQ+F,uBAAyBE,GAAiBS,EAErD,CAEgB,SAAAC,gBAAgBT,GAC/B,MAAMU,EAAWzG,EAAO+F,GACxBU,EAASjB,QAAUO,EAEnB9F,EAAU,IACFG,EAAO,WACb,IAAKoC,EAAWA,EAAYhB,KAAKiB,EACjCjB,KAAKiB,EAAU4D,EACf,OAAOI,EAASjB,SACjB,GACE,GACJ,QAAAH,YAAAjE,UAAAoF"}