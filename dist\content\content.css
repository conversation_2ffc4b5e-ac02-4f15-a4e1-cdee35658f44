/* 防止影响页面原有样式，使用高特定性选择器或唯一ID */
#my-extension-sidebar-xyz {
  position: fixed;
  top: 0;
  right: -320px; /* 初始隐藏在屏幕外，配合transition */
  width: 300px;
  height: 100%;
  background-color: #f0f0f0;
  border-left: 1px solid #ccc;
  box-shadow: -2px 0 5px rgba(0,0,0,0.1);
  z-index: 99999999; /* 确保在最上层 */
  display: flex;
  flex-direction: column;
  transition: right 0.3s ease-in-out; /* 平滑过渡效果 */
  font-family: sans-serif; /* 避免继承页面奇怪的字体 */
  color: #333; /* 默认文字颜色 */
}

#my-extension-sidebar-xyz.my-extension-sidebar-visible {
  right: 0; /* 滑入屏幕 */
}

#my-extension-sidebar-xyz.my-extension-sidebar-hidden {
  right: -320px; /* 滑出屏幕 */
}


#my-extension-sidebar-xyz .my-extension-sidebar-header {
  padding: 10px 15px;
  background-color: #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ccc;
}

#my-extension-sidebar-xyz .my-extension-sidebar-header h3 {
  margin: 0;
  font-size: 1.2em;
}

#my-extension-sidebar-xyz #my-extension-sidebar-close {
  background: none;
  border: none;
  font-size: 1.8em;
  cursor: pointer;
  padding: 0 5px;
  line-height: 1;
  color: #555;
}
#my-extension-sidebar-xyz #my-extension-sidebar-close:hover {
    color: #000;
}

#my-extension-sidebar-xyz .my-extension-sidebar-content {
  padding: 15px;
  overflow-y: auto;
  flex-grow: 1;
}

#my-extension-sidebar-xyz .my-extension-sidebar-content p {
    margin: 0 0 10px 0;
    line-height: 1.6;
}